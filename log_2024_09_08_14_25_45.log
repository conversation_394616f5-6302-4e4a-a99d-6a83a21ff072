2024-09-08 14:25:45,812 - train.py[line:122] - INFO: use random seed
2024-09-08 14:25:45,845 - train.py[line:161] - INFO: val dataset len:656
2024-09-08 14:25:59,938 - train.py[line:171] - INFO: config: 
{   'abs_dir': '/defaultShare/archive/yinbowen/VIT/Finetuning',
    'aux_rate': 0.0,
    'backbone': 'MLP_v4_base',
    'background': 255,
    'batch_size': 8,
    'bn_eps': 0.001,
    'bn_momentum': 0.1,
    'checkpoint_dir': '/defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/checkpoint',
    'checkpoint_start_epoch': 250,
    'checkpoint_step': 25,
    'class_names': [   'wall',
                       'floor',
                       'cabinet',
                       'bed',
                       'chair',
                       'sofa',
                       'table',
                       'door',
                       'window',
                       'bookshelf',
                       'picture',
                       'counter',
                       'blinds',
                       'desk',
                       'shelves',
                       'curtain',
                       'dresser',
                       'pillow',
                       'mirror',
                       'floor mat',
                       'clothes',
                       'ceiling',
                       'books',
                       'refridgerator',
                       'television',
                       'paper',
                       'towel',
                       'shower curtain',
                       'box',
                       'whiteboard',
                       'person',
                       'night stand',
                       'toilet',
                       'sink',
                       'lamp',
                       'bathtub',
                       'bag',
                       'otherstructure',
                       'otherfurniture',
                       'otherprop'],
    'dataset_name': 'NYUDepthv2',
    'dataset_path': 'datasets/NYUDepthv2',
    'decoder': 'ham',
    'decoder_embed_dim': 512,
    'drop_path_rate': 0.5,
    'eval_crop_size': [480, 640],
    'eval_flip': True,
    'eval_iter': 25,
    'eval_scale_array': [1],
    'eval_source': 'datasets/NYUDepthv2/test.txt',
    'eval_stride_rate': 0.6666666666666666,
    'fix_bias': True,
    'gt_format': '.png',
    'gt_root_folder': 'datasets/NYUDepthv2/Label',
    'gt_transform': True,
    'image_height': 480,
    'image_width': 640,
    'is_test': True,
    'link_log_file': '/defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/log_2024_09_08_14_25_45.log/log_last.log',
    'link_val_log_file': '/defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/val_last.log',
    'log_dir': '/defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545',
    'log_dir_link': '/defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545',
    'log_file': '/defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/log_2024_09_08_14_25_45.log',
    'lr': 6e-05,
    'lr_power': 0.9,
    'momentum': 0.9,
    'nepochs': 500,
    'niters_per_epoch': 100,
    'norm_mean': array([0.485, 0.456, 0.406]),
    'norm_std': array([0.229, 0.224, 0.225]),
    'num_classes': 40,
    'num_eval_imgs': 654,
    'num_train_imgs': 795,
    'num_workers': 16,
    'optimizer': 'AdamW',
    'pretrained_model': '/defaultShare/archive/yinbowen/VIT/RGBD_pre_accumulate/outputs/20240826-180638-MLP_v4_base-224/model_best.pth.tar',
    'rgb_format': '.jpg',
    'rgb_root_folder': 'datasets/NYUDepthv2/RGB',
    'root_dir': 'datasets',
    'seed': 12345,
    'tb_dir': '/defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/tb',
    'train_scale_array': [0.5, 0.75, 1, 1.25, 1.5, 1.75],
    'train_source': 'datasets/NYUDepthv2/train.txt',
    'val_log_file': '/defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/val_2024_09_08_14_25_45.log',
    'warm_up_epoch': 10,
    'weight_decay': 0.01,
    'x_format': '.png',
    'x_is_single_channel': True,
    'x_root_folder': 'datasets/NYUDepthv2/Depth'}
2024-09-08 14:25:59,940 - train.py[line:173] - INFO: args parsed:
2024-09-08 14:25:59,940 - train.py[line:175] - INFO: config: local_configs.NYUDepthv2.MLP_v4_base
2024-09-08 14:25:59,940 - train.py[line:175] - INFO: gpus: 4
2024-09-08 14:25:59,941 - train.py[line:175] - INFO: verbose: False
2024-09-08 14:25:59,941 - train.py[line:175] - INFO: epochs: 0
2024-09-08 14:25:59,941 - train.py[line:175] - INFO: show_image: False
2024-09-08 14:25:59,941 - train.py[line:175] - INFO: save_path: None
2024-09-08 14:25:59,941 - train.py[line:175] - INFO: checkpoint_dir: None
2024-09-08 14:25:59,941 - train.py[line:175] - INFO: continue_fpath: None
2024-09-08 14:25:59,941 - train.py[line:175] - INFO: sliding: False
2024-09-08 14:25:59,941 - train.py[line:175] - INFO: compile: False
2024-09-08 14:25:59,941 - train.py[line:175] - INFO: compile_mode: default
2024-09-08 14:25:59,942 - train.py[line:175] - INFO: syncbn: True
2024-09-08 14:25:59,942 - train.py[line:175] - INFO: mst: True
2024-09-08 14:25:59,942 - train.py[line:175] - INFO: amp: False
2024-09-08 14:25:59,942 - train.py[line:175] - INFO: val_amp: True
2024-09-08 14:25:59,942 - train.py[line:175] - INFO: use_seed: False
2024-09-08 14:25:59,942 - train.py[line:175] - INFO: local_rank: 0
2024-09-08 14:25:59,942 - train.py[line:175] - INFO: devices: 
2024-09-08 14:25:59,942 - train.py[line:175] - INFO: port: 16005
2024-09-08 14:25:59,943 - train.py[line:181] - INFO: using syncbn
2024-09-08 14:26:02,334 - builder.py[line:180] - INFO: Using Ham Decoder
2024-09-08 14:26:02,842 - builder.py[line:229] - INFO: Loading pretrained model: /defaultShare/archive/yinbowen/VIT/RGBD_pre_accumulate/outputs/20240826-180638-MLP_v4_base-224/model_best.pth.tar
2024-09-08 14:26:09,846 - builder.py[line:231] - INFO: Initing weights ...
2024-09-08 14:26:09,861 - train.py[line:233] - INFO: .............distributed training.............
2024-09-08 14:26:11,222 - train.py[line:252] - INFO: begin trainning:
2024-09-08 14:27:49,443 - train.py[line:384] - INFO: Epoch 1/500 Iter 100/100: lr=5.9400e-06 loss=4.0304 total_loss=4.0527
2024-09-08 14:35:24,103 - engine.py[line:101] - INFO: Saving checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-1_miou_1.07.pth
2024-09-08 14:35:27,626 - engine.py[line:124] - INFO: Save checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-1_miou_1.07.pth, Time usage:
	prepare checkpoint: 0.014897346496582031, IO: 3.508439540863037
2024-09-08 14:35:27,627 - train.py[line:525] - INFO: Epoch 1 validation result: mIoU 1.07, best mIoU 1.07
2024-09-08 14:35:27,627 - train.py[line:541] - INFO: Avg train time: 97.05s, avg eval time: 458.18s, left eval count: 275, ETA: 2024-09-10 15:02:36
2024-09-08 14:36:39,485 - train.py[line:384] - INFO: Epoch 2/500 Iter 100/100: lr=1.1940e-05 loss=3.6391 total_loss=3.7347
2024-09-08 14:36:39,486 - train.py[line:541] - INFO: Avg train time: 86.70s, avg eval time: 458.18s, left eval count: 275, ETA: 2024-09-10 13:36:14
2024-09-08 14:37:57,142 - train.py[line:384] - INFO: Epoch 3/500 Iter 100/100: lr=1.7940e-05 loss=3.1052 total_loss=3.2172
2024-09-08 14:37:57,142 - train.py[line:541] - INFO: Avg train time: 82.47s, avg eval time: 458.18s, left eval count: 275, ETA: 2024-09-10 13:01:05
2024-09-08 14:39:15,416 - train.py[line:384] - INFO: Epoch 4/500 Iter 100/100: lr=2.3940e-05 loss=2.2077 total_loss=2.6164
2024-09-08 14:39:15,417 - train.py[line:541] - INFO: Avg train time: 80.25s, avg eval time: 458.18s, left eval count: 275, ETA: 2024-09-10 12:42:38
2024-09-08 14:40:32,659 - train.py[line:384] - INFO: Epoch 5/500 Iter 100/100: lr=2.9940e-05 loss=1.8700 total_loss=2.0595
2024-09-08 14:40:32,660 - train.py[line:541] - INFO: Avg train time: 78.65s, avg eval time: 458.18s, left eval count: 275, ETA: 2024-09-10 12:29:23
2024-09-08 14:41:49,324 - train.py[line:384] - INFO: Epoch 6/500 Iter 100/100: lr=3.5940e-05 loss=1.5058 total_loss=1.6950
2024-09-08 14:41:49,325 - train.py[line:541] - INFO: Avg train time: 77.36s, avg eval time: 458.18s, left eval count: 275, ETA: 2024-09-10 12:18:47
2024-09-08 14:43:14,367 - train.py[line:384] - INFO: Epoch 7/500 Iter 100/100: lr=4.1940e-05 loss=1.4801 total_loss=1.4678
2024-09-08 14:43:14,368 - train.py[line:541] - INFO: Avg train time: 79.85s, avg eval time: 458.18s, left eval count: 275, ETA: 2024-09-10 12:39:21
2024-09-08 14:44:32,705 - train.py[line:384] - INFO: Epoch 8/500 Iter 100/100: lr=4.7940e-05 loss=1.5880 total_loss=1.3031
2024-09-08 14:44:32,706 - train.py[line:541] - INFO: Avg train time: 78.83s, avg eval time: 458.18s, left eval count: 275, ETA: 2024-09-10 12:30:58
2024-09-08 14:45:45,121 - train.py[line:384] - INFO: Epoch 9/500 Iter 100/100: lr=5.3940e-05 loss=1.7466 total_loss=1.1699
2024-09-08 14:45:45,122 - train.py[line:541] - INFO: Avg train time: 75.72s, avg eval time: 458.18s, left eval count: 275, ETA: 2024-09-10 12:05:21
2024-09-08 14:47:05,078 - train.py[line:384] - INFO: Epoch 10/500 Iter 100/100: lr=5.9940e-05 loss=1.0241 total_loss=1.0514
2024-09-08 14:54:33,649 - engine.py[line:101] - INFO: Saving checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-10_miou_38.75.pth
2024-09-08 14:54:37,041 - engine.py[line:124] - INFO: Save checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-10_miou_38.75.pth, Time usage:
	prepare checkpoint: 0.014348506927490234, IO: 3.377635955810547
2024-09-08 14:54:37,042 - train.py[line:525] - INFO: Epoch 10 validation result: mIoU 38.75, best mIoU 38.75
2024-09-08 14:54:37,042 - train.py[line:541] - INFO: Avg train time: 76.82s, avg eval time: 455.70s, left eval count: 274, ETA: 2024-09-10 12:03:00
2024-09-08 14:55:57,303 - train.py[line:384] - INFO: Epoch 11/500 Iter 100/100: lr=5.8812e-05 loss=0.9729 total_loss=0.9774
2024-09-08 14:55:57,304 - train.py[line:541] - INFO: Avg train time: 77.95s, avg eval time: 455.70s, left eval count: 274, ETA: 2024-09-10 12:12:16
2024-09-08 14:57:19,804 - train.py[line:384] - INFO: Epoch 12/500 Iter 100/100: lr=5.8704e-05 loss=0.7564 total_loss=0.8785
2024-09-08 14:57:19,805 - train.py[line:541] - INFO: Avg train time: 79.29s, avg eval time: 455.70s, left eval count: 274, ETA: 2024-09-10 12:23:16
2024-09-08 14:58:30,981 - train.py[line:384] - INFO: Epoch 13/500 Iter 100/100: lr=5.8595e-05 loss=0.6255 total_loss=0.8189
2024-09-08 14:58:30,982 - train.py[line:541] - INFO: Avg train time: 75.49s, avg eval time: 455.70s, left eval count: 274, ETA: 2024-09-10 11:52:16
2024-09-08 14:59:57,335 - train.py[line:384] - INFO: Epoch 14/500 Iter 100/100: lr=5.8487e-05 loss=0.5832 total_loss=0.7813
2024-09-08 14:59:57,336 - train.py[line:541] - INFO: Avg train time: 79.31s, avg eval time: 455.70s, left eval count: 274, ETA: 2024-09-10 12:23:20
2024-09-08 15:01:23,295 - train.py[line:384] - INFO: Epoch 15/500 Iter 100/100: lr=5.8379e-05 loss=0.5569 total_loss=0.7228
2024-09-08 15:01:23,296 - train.py[line:541] - INFO: Avg train time: 81.50s, avg eval time: 455.70s, left eval count: 274, ETA: 2024-09-10 12:41:11
2024-09-08 15:02:36,433 - train.py[line:384] - INFO: Epoch 16/500 Iter 100/100: lr=5.8270e-05 loss=0.6039 total_loss=0.6822
2024-09-08 15:02:36,434 - train.py[line:541] - INFO: Avg train time: 77.59s, avg eval time: 455.70s, left eval count: 274, ETA: 2024-09-10 12:09:30
2024-09-08 15:04:01,037 - train.py[line:384] - INFO: Epoch 17/500 Iter 100/100: lr=5.8162e-05 loss=0.4792 total_loss=0.6399
2024-09-08 15:04:01,037 - train.py[line:541] - INFO: Avg train time: 79.97s, avg eval time: 455.70s, left eval count: 274, ETA: 2024-09-10 12:28:44
2024-09-08 15:05:24,036 - train.py[line:384] - INFO: Epoch 18/500 Iter 100/100: lr=5.8054e-05 loss=0.7927 total_loss=0.6203
2024-09-08 15:05:24,036 - train.py[line:541] - INFO: Avg train time: 80.60s, avg eval time: 455.70s, left eval count: 274, ETA: 2024-09-10 12:33:53
2024-09-08 15:06:42,673 - train.py[line:384] - INFO: Epoch 19/500 Iter 100/100: lr=5.7945e-05 loss=0.6099 total_loss=0.5880
2024-09-08 15:06:42,674 - train.py[line:541] - INFO: Avg train time: 79.19s, avg eval time: 455.70s, left eval count: 274, ETA: 2024-09-10 12:22:32
2024-09-08 15:07:49,367 - train.py[line:384] - INFO: Epoch 20/500 Iter 100/100: lr=5.7837e-05 loss=0.6861 total_loss=0.5835
2024-09-08 15:15:18,448 - engine.py[line:101] - INFO: Saving checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-20_miou_49.15.pth
2024-09-08 15:15:21,921 - engine.py[line:124] - INFO: Save checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-20_miou_49.15.pth, Time usage:
	prepare checkpoint: 0.014210700988769531, IO: 3.4584546089172363
2024-09-08 15:15:21,922 - train.py[line:525] - INFO: Epoch 20 validation result: mIoU 49.15, best mIoU 49.15
2024-09-08 15:15:21,923 - train.py[line:541] - INFO: Avg train time: 73.54s, avg eval time: 454.44s, left eval count: 273, ETA: 2024-09-10 11:31:21
2024-09-08 15:16:44,183 - train.py[line:384] - INFO: Epoch 21/500 Iter 100/100: lr=5.7728e-05 loss=0.6372 total_loss=0.5378
2024-09-08 15:16:44,184 - train.py[line:541] - INFO: Avg train time: 76.74s, avg eval time: 454.44s, left eval count: 273, ETA: 2024-09-10 11:57:05
2024-09-08 15:18:03,130 - train.py[line:384] - INFO: Epoch 22/500 Iter 100/100: lr=5.7620e-05 loss=0.3724 total_loss=0.5258
2024-09-08 15:18:03,131 - train.py[line:541] - INFO: Avg train time: 76.97s, avg eval time: 454.44s, left eval count: 273, ETA: 2024-09-10 11:58:57
2024-09-08 15:19:14,369 - train.py[line:384] - INFO: Epoch 23/500 Iter 100/100: lr=5.7511e-05 loss=0.4269 total_loss=0.4912
2024-09-08 15:19:14,369 - train.py[line:541] - INFO: Avg train time: 74.21s, avg eval time: 454.44s, left eval count: 273, ETA: 2024-09-10 11:36:53
2024-09-08 15:20:27,968 - train.py[line:384] - INFO: Epoch 24/500 Iter 100/100: lr=5.7403e-05 loss=0.4062 total_loss=0.4647
2024-09-08 15:20:27,969 - train.py[line:541] - INFO: Avg train time: 73.51s, avg eval time: 454.44s, left eval count: 273, ETA: 2024-09-10 11:31:20
2024-09-08 15:21:44,247 - train.py[line:384] - INFO: Epoch 25/500 Iter 100/100: lr=5.7294e-05 loss=0.5192 total_loss=0.4607
2024-09-08 15:21:44,248 - train.py[line:541] - INFO: Avg train time: 74.04s, avg eval time: 454.44s, left eval count: 273, ETA: 2024-09-10 11:35:36
2024-09-08 15:23:02,786 - train.py[line:384] - INFO: Epoch 26/500 Iter 100/100: lr=5.7186e-05 loss=0.5113 total_loss=0.4438
2024-09-08 15:23:02,787 - train.py[line:541] - INFO: Avg train time: 75.30s, avg eval time: 454.44s, left eval count: 273, ETA: 2024-09-10 11:45:37
2024-09-08 15:24:25,572 - train.py[line:384] - INFO: Epoch 27/500 Iter 100/100: lr=5.7077e-05 loss=0.3219 total_loss=0.4292
2024-09-08 15:24:25,572 - train.py[line:541] - INFO: Avg train time: 77.66s, avg eval time: 454.44s, left eval count: 273, ETA: 2024-09-10 12:04:19
2024-09-08 15:25:52,806 - train.py[line:384] - INFO: Epoch 28/500 Iter 100/100: lr=5.6968e-05 loss=0.4204 total_loss=0.3965
2024-09-08 15:25:52,807 - train.py[line:541] - INFO: Avg train time: 81.03s, avg eval time: 454.44s, left eval count: 273, ETA: 2024-09-10 12:31:02
2024-09-08 15:27:16,128 - train.py[line:384] - INFO: Epoch 29/500 Iter 100/100: lr=5.6860e-05 loss=0.4527 total_loss=0.3933
2024-09-08 15:27:16,129 - train.py[line:541] - INFO: Avg train time: 81.32s, avg eval time: 454.44s, left eval count: 273, ETA: 2024-09-10 12:33:17
2024-09-08 15:28:24,998 - train.py[line:384] - INFO: Epoch 30/500 Iter 100/100: lr=5.6751e-05 loss=0.6624 total_loss=0.3758
2024-09-08 15:35:54,383 - engine.py[line:101] - INFO: Saving checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-30_miou_52.85.pth
2024-09-08 15:35:57,836 - engine.py[line:124] - INFO: Save checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-30_miou_52.85.pth, Time usage:
	prepare checkpoint: 0.011464118957519531, IO: 3.4408926963806152
2024-09-08 15:35:57,836 - train.py[line:525] - INFO: Epoch 30 validation result: mIoU 52.85, best mIoU 52.85
2024-09-08 15:35:57,837 - train.py[line:541] - INFO: Avg train time: 75.75s, avg eval time: 453.80s, left eval count: 272, ETA: 2024-09-10 11:46:34
2024-09-08 15:37:16,473 - train.py[line:384] - INFO: Epoch 31/500 Iter 100/100: lr=5.6642e-05 loss=0.4012 total_loss=0.3860
2024-09-08 15:37:16,473 - train.py[line:541] - INFO: Avg train time: 76.64s, avg eval time: 453.80s, left eval count: 272, ETA: 2024-09-10 11:53:34
2024-09-08 15:38:37,038 - train.py[line:384] - INFO: Epoch 32/500 Iter 100/100: lr=5.6534e-05 loss=0.3782 total_loss=0.3594
2024-09-08 15:38:37,039 - train.py[line:541] - INFO: Avg train time: 77.64s, avg eval time: 453.80s, left eval count: 272, ETA: 2024-09-10 12:01:23
2024-09-08 15:40:02,207 - train.py[line:384] - INFO: Epoch 33/500 Iter 100/100: lr=5.6425e-05 loss=0.3587 total_loss=0.3518
2024-09-08 15:40:02,208 - train.py[line:541] - INFO: Avg train time: 80.06s, avg eval time: 453.80s, left eval count: 272, ETA: 2024-09-10 12:20:25
2024-09-08 15:41:27,898 - train.py[line:384] - INFO: Epoch 34/500 Iter 100/100: lr=5.6316e-05 loss=0.4063 total_loss=0.3434
2024-09-08 15:41:27,899 - train.py[line:541] - INFO: Avg train time: 81.80s, avg eval time: 453.80s, left eval count: 272, ETA: 2024-09-10 12:34:00
2024-09-08 15:42:54,125 - train.py[line:384] - INFO: Epoch 35/500 Iter 100/100: lr=5.6208e-05 loss=0.4191 total_loss=0.3219
2024-09-08 15:42:54,125 - train.py[line:541] - INFO: Avg train time: 83.13s, avg eval time: 453.80s, left eval count: 272, ETA: 2024-09-10 12:44:22
2024-09-08 15:44:20,191 - train.py[line:384] - INFO: Epoch 36/500 Iter 100/100: lr=5.6099e-05 loss=0.4229 total_loss=0.3163
2024-09-08 15:44:20,191 - train.py[line:541] - INFO: Avg train time: 83.79s, avg eval time: 453.80s, left eval count: 272, ETA: 2024-09-10 12:49:31
2024-09-08 15:45:44,903 - train.py[line:384] - INFO: Epoch 37/500 Iter 100/100: lr=5.5990e-05 loss=0.4987 total_loss=0.3158
2024-09-08 15:45:44,903 - train.py[line:541] - INFO: Avg train time: 83.55s, avg eval time: 453.80s, left eval count: 272, ETA: 2024-09-10 12:47:43
2024-09-08 15:46:57,981 - train.py[line:384] - INFO: Epoch 38/500 Iter 100/100: lr=5.5881e-05 loss=0.2799 total_loss=0.2902
2024-09-08 15:46:57,981 - train.py[line:541] - INFO: Avg train time: 78.93s, avg eval time: 453.80s, left eval count: 272, ETA: 2024-09-10 12:11:57
2024-09-08 15:48:07,785 - train.py[line:384] - INFO: Epoch 39/500 Iter 100/100: lr=5.5772e-05 loss=0.3020 total_loss=0.3022
2024-09-08 15:48:07,786 - train.py[line:541] - INFO: Avg train time: 74.68s, avg eval time: 453.80s, left eval count: 272, ETA: 2024-09-10 11:39:07
2024-09-08 15:49:18,839 - train.py[line:384] - INFO: Epoch 40/500 Iter 100/100: lr=5.5663e-05 loss=0.3144 total_loss=0.2916
2024-09-08 15:56:46,963 - engine.py[line:101] - INFO: Saving checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-40_miou_54.38.pth
2024-09-08 15:56:50,181 - engine.py[line:124] - INFO: Save checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-40_miou_54.38.pth, Time usage:
	prepare checkpoint: 0.011479616165161133, IO: 3.206258535385132
2024-09-08 15:56:50,182 - train.py[line:525] - INFO: Epoch 40 validation result: mIoU 54.38, best mIoU 54.38
2024-09-08 15:56:50,182 - train.py[line:541] - INFO: Avg train time: 72.59s, avg eval time: 452.82s, left eval count: 271, ETA: 2024-09-10 11:18:33
2024-09-08 15:58:07,812 - train.py[line:384] - INFO: Epoch 41/500 Iter 100/100: lr=5.5554e-05 loss=0.2282 total_loss=0.2769
2024-09-08 15:58:07,812 - train.py[line:541] - INFO: Avg train time: 74.33s, avg eval time: 452.82s, left eval count: 271, ETA: 2024-09-10 11:31:59
2024-09-08 15:59:28,580 - train.py[line:384] - INFO: Epoch 42/500 Iter 100/100: lr=5.5445e-05 loss=0.2453 total_loss=0.2807
2024-09-08 15:59:28,580 - train.py[line:541] - INFO: Avg train time: 76.51s, avg eval time: 452.82s, left eval count: 271, ETA: 2024-09-10 11:48:45
2024-09-08 16:00:50,644 - train.py[line:384] - INFO: Epoch 43/500 Iter 100/100: lr=5.5336e-05 loss=0.2997 total_loss=0.2941
2024-09-08 16:00:50,645 - train.py[line:541] - INFO: Avg train time: 78.21s, avg eval time: 452.82s, left eval count: 271, ETA: 2024-09-10 12:01:46
2024-09-08 16:02:05,910 - train.py[line:384] - INFO: Epoch 44/500 Iter 100/100: lr=5.5227e-05 loss=0.2882 total_loss=0.2701
2024-09-08 16:02:05,911 - train.py[line:541] - INFO: Avg train time: 76.41s, avg eval time: 452.82s, left eval count: 271, ETA: 2024-09-10 11:48:02
2024-09-08 16:03:19,212 - train.py[line:384] - INFO: Epoch 45/500 Iter 100/100: lr=5.5118e-05 loss=0.2575 total_loss=0.2600
2024-09-08 16:03:19,213 - train.py[line:541] - INFO: Avg train time: 74.46s, avg eval time: 452.82s, left eval count: 271, ETA: 2024-09-10 11:33:09
2024-09-08 16:04:47,960 - train.py[line:384] - INFO: Epoch 46/500 Iter 100/100: lr=5.5009e-05 loss=0.3162 total_loss=0.2655
2024-09-08 16:04:47,961 - train.py[line:541] - INFO: Avg train time: 79.63s, avg eval time: 452.82s, left eval count: 271, ETA: 2024-09-10 12:12:31
2024-09-08 16:06:08,199 - train.py[line:384] - INFO: Epoch 47/500 Iter 100/100: lr=5.4900e-05 loss=0.2311 total_loss=0.2391
2024-09-08 16:06:08,199 - train.py[line:541] - INFO: Avg train time: 79.46s, avg eval time: 452.82s, left eval count: 271, ETA: 2024-09-10 12:11:16
2024-09-08 16:07:32,626 - train.py[line:384] - INFO: Epoch 48/500 Iter 100/100: lr=5.4791e-05 loss=0.4032 total_loss=0.2530
2024-09-08 16:07:32,627 - train.py[line:541] - INFO: Avg train time: 80.80s, avg eval time: 452.82s, left eval count: 271, ETA: 2024-09-10 12:21:29
2024-09-08 16:08:44,193 - train.py[line:384] - INFO: Epoch 49/500 Iter 100/100: lr=5.4682e-05 loss=0.1993 total_loss=0.2495
2024-09-08 16:08:44,193 - train.py[line:541] - INFO: Avg train time: 76.68s, avg eval time: 452.82s, left eval count: 271, ETA: 2024-09-10 11:50:18
2024-09-08 16:10:00,874 - train.py[line:384] - INFO: Epoch 50/500 Iter 100/100: lr=5.4573e-05 loss=0.2651 total_loss=0.2288
2024-09-08 16:17:31,055 - engine.py[line:153] - INFO: remove inferior checkpoint: {'epoch': 1, 'metric': 1.07}
2024-09-08 16:17:31,056 - engine.py[line:101] - INFO: Saving checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-50_miou_55.05.pth
2024-09-08 16:17:34,190 - engine.py[line:124] - INFO: Save checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-50_miou_55.05.pth, Time usage:
	prepare checkpoint: 0.010382652282714844, IO: 3.1231818199157715
2024-09-08 16:17:34,190 - train.py[line:525] - INFO: Epoch 50 validation result: mIoU 55.05, best mIoU 55.05
2024-09-08 16:17:34,191 - train.py[line:541] - INFO: Avg train time: 76.04s, avg eval time: 453.02s, left eval count: 270, ETA: 2024-09-10 11:46:27
2024-09-08 16:18:53,544 - train.py[line:384] - INFO: Epoch 51/500 Iter 100/100: lr=5.4464e-05 loss=0.2349 total_loss=0.2236
2024-09-08 16:18:53,545 - train.py[line:541] - INFO: Avg train time: 77.05s, avg eval time: 453.02s, left eval count: 270, ETA: 2024-09-10 11:54:02
2024-09-08 16:20:10,748 - train.py[line:384] - INFO: Epoch 52/500 Iter 100/100: lr=5.4355e-05 loss=0.3068 total_loss=0.2399
2024-09-08 16:20:10,749 - train.py[line:541] - INFO: Avg train time: 76.73s, avg eval time: 453.02s, left eval count: 270, ETA: 2024-09-10 11:51:40
2024-09-08 16:21:39,952 - train.py[line:384] - INFO: Epoch 53/500 Iter 100/100: lr=5.4246e-05 loss=0.2320 total_loss=0.2297
2024-09-08 16:21:39,952 - train.py[line:541] - INFO: Avg train time: 81.14s, avg eval time: 453.02s, left eval count: 270, ETA: 2024-09-10 12:24:43
2024-09-08 16:22:59,274 - train.py[line:384] - INFO: Epoch 54/500 Iter 100/100: lr=5.4136e-05 loss=0.2234 total_loss=0.2325
2024-09-08 16:22:59,275 - train.py[line:541] - INFO: Avg train time: 79.80s, avg eval time: 453.02s, left eval count: 270, ETA: 2024-09-10 12:14:44
2024-09-08 16:24:07,613 - train.py[line:384] - INFO: Epoch 55/500 Iter 100/100: lr=5.4027e-05 loss=0.1721 total_loss=0.2137
2024-09-08 16:24:07,613 - train.py[line:541] - INFO: Avg train time: 74.60s, avg eval time: 453.02s, left eval count: 270, ETA: 2024-09-10 11:35:59
2024-09-08 16:25:24,456 - train.py[line:384] - INFO: Epoch 56/500 Iter 100/100: lr=5.3918e-05 loss=0.2504 total_loss=0.2099
2024-09-08 16:25:24,457 - train.py[line:541] - INFO: Avg train time: 74.83s, avg eval time: 453.02s, left eval count: 270, ETA: 2024-09-10 11:37:42
2024-09-08 16:26:35,497 - train.py[line:384] - INFO: Epoch 57/500 Iter 100/100: lr=5.3808e-05 loss=0.1582 total_loss=0.1936
2024-09-08 16:26:35,497 - train.py[line:541] - INFO: Avg train time: 72.69s, avg eval time: 453.02s, left eval count: 270, ETA: 2024-09-10 11:21:53
2024-09-08 16:28:04,124 - train.py[line:384] - INFO: Epoch 58/500 Iter 100/100: lr=5.3699e-05 loss=0.1414 total_loss=0.1898
2024-09-08 16:28:04,125 - train.py[line:541] - INFO: Avg train time: 78.50s, avg eval time: 453.02s, left eval count: 270, ETA: 2024-09-10 12:04:57
2024-09-08 16:29:28,761 - train.py[line:384] - INFO: Epoch 59/500 Iter 100/100: lr=5.3590e-05 loss=0.2258 total_loss=0.1853
2024-09-08 16:29:28,762 - train.py[line:541] - INFO: Avg train time: 80.37s, avg eval time: 453.02s, left eval count: 270, ETA: 2024-09-10 12:18:48
2024-09-08 16:30:51,342 - train.py[line:384] - INFO: Epoch 60/500 Iter 100/100: lr=5.3480e-05 loss=0.1553 total_loss=0.1892
2024-09-08 16:38:20,484 - engine.py[line:153] - INFO: remove inferior checkpoint: {'epoch': 10, 'metric': 38.75}
2024-09-08 16:38:20,485 - engine.py[line:101] - INFO: Saving checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-60_miou_56.07.pth
2024-09-08 16:38:24,396 - engine.py[line:124] - INFO: Save checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-60_miou_56.07.pth, Time usage:
	prepare checkpoint: 0.011418342590332031, IO: 3.8999712467193604
2024-09-08 16:38:24,397 - train.py[line:525] - INFO: Epoch 60 validation result: mIoU 56.07, best mIoU 56.07
2024-09-08 16:38:24,397 - train.py[line:541] - INFO: Avg train time: 80.76s, avg eval time: 453.03s, left eval count: 269, ETA: 2024-09-10 12:21:44
2024-09-08 16:39:34,200 - train.py[line:384] - INFO: Epoch 61/500 Iter 100/100: lr=5.3371e-05 loss=0.1794 total_loss=0.2075
2024-09-08 16:39:34,201 - train.py[line:541] - INFO: Avg train time: 76.08s, avg eval time: 453.03s, left eval count: 269, ETA: 2024-09-10 11:47:18
2024-09-08 16:40:48,589 - train.py[line:384] - INFO: Epoch 62/500 Iter 100/100: lr=5.3262e-05 loss=0.2096 total_loss=0.2062
2024-09-08 16:40:48,590 - train.py[line:541] - INFO: Avg train time: 74.85s, avg eval time: 453.03s, left eval count: 269, ETA: 2024-09-10 11:38:19
2024-09-08 16:41:56,981 - train.py[line:384] - INFO: Epoch 63/500 Iter 100/100: lr=5.3152e-05 loss=0.0911 total_loss=0.1814
2024-09-08 16:41:56,981 - train.py[line:541] - INFO: Avg train time: 71.63s, avg eval time: 453.03s, left eval count: 269, ETA: 2024-09-10 11:14:43
2024-09-08 16:43:16,141 - train.py[line:384] - INFO: Epoch 64/500 Iter 100/100: lr=5.3043e-05 loss=0.2591 total_loss=0.1921
2024-09-08 16:43:16,142 - train.py[line:541] - INFO: Avg train time: 74.16s, avg eval time: 453.03s, left eval count: 269, ETA: 2024-09-10 11:33:15
2024-09-08 16:44:42,433 - train.py[line:384] - INFO: Epoch 65/500 Iter 100/100: lr=5.2933e-05 loss=0.1846 total_loss=0.1773
2024-09-08 16:44:42,434 - train.py[line:541] - INFO: Avg train time: 78.48s, avg eval time: 453.03s, left eval count: 269, ETA: 2024-09-10 12:04:45
2024-09-08 16:46:00,585 - train.py[line:384] - INFO: Epoch 66/500 Iter 100/100: lr=5.2824e-05 loss=0.1623 total_loss=0.1982
2024-09-08 16:46:00,586 - train.py[line:541] - INFO: Avg train time: 77.96s, avg eval time: 453.03s, left eval count: 269, ETA: 2024-09-10 12:01:01
2024-09-08 16:47:14,445 - train.py[line:384] - INFO: Epoch 67/500 Iter 100/100: lr=5.2714e-05 loss=0.1996 total_loss=0.2003
2024-09-08 16:47:14,445 - train.py[line:541] - INFO: Avg train time: 75.64s, avg eval time: 453.03s, left eval count: 269, ETA: 2024-09-10 11:44:13
2024-09-08 16:48:38,742 - train.py[line:384] - INFO: Epoch 68/500 Iter 100/100: lr=5.2604e-05 loss=0.2083 total_loss=0.1907
2024-09-08 16:48:38,743 - train.py[line:541] - INFO: Avg train time: 78.57s, avg eval time: 453.03s, left eval count: 269, ETA: 2024-09-10 12:05:24
2024-09-08 16:50:01,936 - train.py[line:384] - INFO: Epoch 69/500 Iter 100/100: lr=5.2495e-05 loss=0.1652 total_loss=0.1775
2024-09-08 16:50:01,936 - train.py[line:541] - INFO: Avg train time: 79.79s, avg eval time: 453.03s, left eval count: 269, ETA: 2024-09-10 12:14:18
2024-09-08 16:51:22,958 - train.py[line:384] - INFO: Epoch 70/500 Iter 100/100: lr=5.2385e-05 loss=0.1175 total_loss=0.1745
2024-09-08 16:58:51,941 - train.py[line:525] - INFO: Epoch 70 validation result: mIoU 55.79, best mIoU 56.07
2024-09-08 16:58:51,941 - train.py[line:541] - INFO: Avg train time: 79.67s, avg eval time: 451.41s, left eval count: 268, ETA: 2024-09-10 12:06:07
2024-09-08 17:00:05,064 - train.py[line:384] - INFO: Epoch 71/500 Iter 100/100: lr=5.2276e-05 loss=0.2119 total_loss=0.1696
2024-09-08 17:00:05,065 - train.py[line:541] - INFO: Avg train time: 76.62s, avg eval time: 451.41s, left eval count: 268, ETA: 2024-09-10 11:44:12
2024-09-08 17:01:12,038 - train.py[line:384] - INFO: Epoch 72/500 Iter 100/100: lr=5.2166e-05 loss=0.1366 total_loss=0.1677
2024-09-08 17:01:12,039 - train.py[line:541] - INFO: Avg train time: 72.22s, avg eval time: 451.41s, left eval count: 268, ETA: 2024-09-10 11:12:42
2024-09-08 17:02:37,197 - train.py[line:384] - INFO: Epoch 73/500 Iter 100/100: lr=5.2056e-05 loss=0.1414 total_loss=0.1717
2024-09-08 17:02:37,198 - train.py[line:541] - INFO: Avg train time: 76.73s, avg eval time: 451.41s, left eval count: 268, ETA: 2024-09-10 11:44:57
2024-09-08 17:03:47,608 - train.py[line:384] - INFO: Epoch 74/500 Iter 100/100: lr=5.1946e-05 loss=0.1440 total_loss=0.1599
2024-09-08 17:03:47,608 - train.py[line:541] - INFO: Avg train time: 73.47s, avg eval time: 451.41s, left eval count: 268, ETA: 2024-09-10 11:21:43
2024-09-08 17:05:16,353 - train.py[line:384] - INFO: Epoch 75/500 Iter 100/100: lr=5.1837e-05 loss=0.1462 total_loss=0.1639
2024-09-08 17:05:16,355 - train.py[line:541] - INFO: Avg train time: 79.03s, avg eval time: 451.41s, left eval count: 268, ETA: 2024-09-10 12:01:21
2024-09-08 17:06:37,628 - train.py[line:384] - INFO: Epoch 76/500 Iter 100/100: lr=5.1727e-05 loss=0.1497 total_loss=0.1630
2024-09-08 17:06:37,629 - train.py[line:541] - INFO: Avg train time: 79.21s, avg eval time: 451.41s, left eval count: 268, ETA: 2024-09-10 12:02:39
2024-09-08 17:08:00,409 - train.py[line:384] - INFO: Epoch 77/500 Iter 100/100: lr=5.1617e-05 loss=0.1410 total_loss=0.1450
2024-09-08 17:08:00,410 - train.py[line:541] - INFO: Avg train time: 80.01s, avg eval time: 451.41s, left eval count: 268, ETA: 2024-09-10 12:08:22
2024-09-08 17:09:24,085 - train.py[line:384] - INFO: Epoch 78/500 Iter 100/100: lr=5.1507e-05 loss=0.1381 total_loss=0.1466
2024-09-08 17:09:24,086 - train.py[line:541] - INFO: Avg train time: 81.11s, avg eval time: 451.41s, left eval count: 268, ETA: 2024-09-10 12:16:09
2024-09-08 17:10:47,242 - train.py[line:384] - INFO: Epoch 79/500 Iter 100/100: lr=5.1397e-05 loss=0.1728 total_loss=0.1508
2024-09-08 17:10:47,242 - train.py[line:541] - INFO: Avg train time: 81.48s, avg eval time: 451.41s, left eval count: 268, ETA: 2024-09-10 12:18:49
2024-09-08 17:11:57,071 - train.py[line:384] - INFO: Epoch 80/500 Iter 100/100: lr=5.1288e-05 loss=0.1136 total_loss=0.1502
2024-09-08 17:19:26,625 - engine.py[line:153] - INFO: remove inferior checkpoint: {'epoch': 20, 'metric': 49.15}
2024-09-08 17:19:26,626 - engine.py[line:101] - INFO: Saving checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-80_miou_57.1.pth
2024-09-08 17:19:29,836 - engine.py[line:124] - INFO: Save checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-80_miou_57.1.pth, Time usage:
	prepare checkpoint: 0.01045989990234375, IO: 3.199864625930786
2024-09-08 17:19:29,837 - train.py[line:525] - INFO: Epoch 80 validation result: mIoU 57.1, best mIoU 57.1
2024-09-08 17:19:29,837 - train.py[line:541] - INFO: Avg train time: 76.24s, avg eval time: 451.95s, left eval count: 267, ETA: 2024-09-10 11:44:22
2024-09-08 17:20:36,939 - train.py[line:384] - INFO: Epoch 81/500 Iter 100/100: lr=5.1178e-05 loss=0.1993 total_loss=0.1437
2024-09-08 17:20:36,940 - train.py[line:541] - INFO: Avg train time: 72.33s, avg eval time: 451.95s, left eval count: 267, ETA: 2024-09-10 11:16:53
2024-09-08 17:21:57,822 - train.py[line:384] - INFO: Epoch 82/500 Iter 100/100: lr=5.1068e-05 loss=0.1681 total_loss=0.1470
2024-09-08 17:21:57,823 - train.py[line:541] - INFO: Avg train time: 75.17s, avg eval time: 451.95s, left eval count: 267, ETA: 2024-09-10 11:36:49
2024-09-08 17:23:18,477 - train.py[line:384] - INFO: Epoch 83/500 Iter 100/100: lr=5.0958e-05 loss=0.1086 total_loss=0.1370
2024-09-08 17:23:18,478 - train.py[line:541] - INFO: Avg train time: 76.77s, avg eval time: 451.95s, left eval count: 267, ETA: 2024-09-10 11:48:03
2024-09-08 17:24:39,139 - train.py[line:384] - INFO: Epoch 84/500 Iter 100/100: lr=5.0848e-05 loss=0.1043 total_loss=0.1435
2024-09-08 17:24:39,140 - train.py[line:541] - INFO: Avg train time: 77.77s, avg eval time: 451.95s, left eval count: 267, ETA: 2024-09-10 11:55:01
2024-09-08 17:25:56,175 - train.py[line:384] - INFO: Epoch 85/500 Iter 100/100: lr=5.0738e-05 loss=0.1238 total_loss=0.1485
2024-09-08 17:25:56,176 - train.py[line:541] - INFO: Avg train time: 77.04s, avg eval time: 451.95s, left eval count: 267, ETA: 2024-09-10 11:50:00
2024-09-08 17:27:06,757 - train.py[line:384] - INFO: Epoch 86/500 Iter 100/100: lr=5.0628e-05 loss=0.1489 total_loss=0.1390
2024-09-08 17:27:06,758 - train.py[line:541] - INFO: Avg train time: 73.92s, avg eval time: 451.95s, left eval count: 267, ETA: 2024-09-10 11:28:22
2024-09-08 17:28:26,312 - train.py[line:384] - INFO: Epoch 87/500 Iter 100/100: lr=5.0518e-05 loss=0.0809 total_loss=0.1454
2024-09-08 17:28:26,313 - train.py[line:541] - INFO: Avg train time: 75.48s, avg eval time: 451.95s, left eval count: 267, ETA: 2024-09-10 11:39:09
2024-09-08 17:29:52,688 - train.py[line:384] - INFO: Epoch 88/500 Iter 100/100: lr=5.0408e-05 loss=0.2801 total_loss=0.1539
2024-09-08 17:29:52,689 - train.py[line:541] - INFO: Avg train time: 79.20s, avg eval time: 451.95s, left eval count: 267, ETA: 2024-09-10 12:04:54
2024-09-08 17:31:04,524 - train.py[line:384] - INFO: Epoch 89/500 Iter 100/100: lr=5.0297e-05 loss=0.1507 total_loss=0.1757
2024-09-08 17:31:04,524 - train.py[line:541] - INFO: Avg train time: 75.69s, avg eval time: 451.95s, left eval count: 267, ETA: 2024-09-10 11:40:43
2024-09-08 17:32:30,815 - train.py[line:384] - INFO: Epoch 90/500 Iter 100/100: lr=5.0187e-05 loss=0.1314 total_loss=0.1480
2024-09-08 17:40:00,050 - engine.py[line:153] - INFO: remove inferior checkpoint: {'epoch': 30, 'metric': 52.85}
2024-09-08 17:40:00,051 - engine.py[line:101] - INFO: Saving checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-90_miou_57.48.pth
2024-09-08 17:40:02,862 - engine.py[line:124] - INFO: Save checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-90_miou_57.48.pth, Time usage:
	prepare checkpoint: 0.010370254516601562, IO: 2.8004791736602783
2024-09-08 17:40:02,863 - train.py[line:525] - INFO: Epoch 90 validation result: mIoU 57.48, best mIoU 57.48
2024-09-08 17:40:02,863 - train.py[line:541] - INFO: Avg train time: 79.45s, avg eval time: 451.99s, left eval count: 266, ETA: 2024-09-10 12:06:46
2024-09-08 17:41:09,440 - train.py[line:384] - INFO: Epoch 91/500 Iter 100/100: lr=5.0077e-05 loss=0.1114 total_loss=0.1523
2024-09-08 17:41:09,441 - train.py[line:541] - INFO: Avg train time: 74.04s, avg eval time: 451.99s, left eval count: 266, ETA: 2024-09-10 11:29:40
2024-09-08 17:42:25,169 - train.py[line:384] - INFO: Epoch 92/500 Iter 100/100: lr=4.9967e-05 loss=0.1152 total_loss=0.1348
2024-09-08 17:42:25,169 - train.py[line:541] - INFO: Avg train time: 74.13s, avg eval time: 451.99s, left eval count: 266, ETA: 2024-09-10 11:30:18
2024-09-08 17:43:31,958 - train.py[line:384] - INFO: Epoch 93/500 Iter 100/100: lr=4.9857e-05 loss=0.1275 total_loss=0.1390
2024-09-08 17:43:31,959 - train.py[line:541] - INFO: Avg train time: 70.62s, avg eval time: 451.99s, left eval count: 266, ETA: 2024-09-10 11:06:22
2024-09-08 17:44:39,925 - train.py[line:384] - INFO: Epoch 94/500 Iter 100/100: lr=4.9746e-05 loss=0.1433 total_loss=0.1293
2024-09-08 17:44:39,926 - train.py[line:541] - INFO: Avg train time: 68.94s, avg eval time: 451.99s, left eval count: 266, ETA: 2024-09-10 10:54:58
2024-09-08 17:45:59,907 - train.py[line:384] - INFO: Epoch 95/500 Iter 100/100: lr=4.9636e-05 loss=0.1541 total_loss=0.1285
2024-09-08 17:45:59,907 - train.py[line:541] - INFO: Avg train time: 72.72s, avg eval time: 451.99s, left eval count: 266, ETA: 2024-09-10 11:20:41
2024-09-08 17:47:23,150 - train.py[line:384] - INFO: Epoch 96/500 Iter 100/100: lr=4.9526e-05 loss=0.1162 total_loss=0.1443
2024-09-08 17:47:23,151 - train.py[line:541] - INFO: Avg train time: 76.26s, avg eval time: 451.99s, left eval count: 266, ETA: 2024-09-10 11:44:40
2024-09-08 17:48:38,918 - train.py[line:384] - INFO: Epoch 97/500 Iter 100/100: lr=4.9415e-05 loss=0.2274 total_loss=0.1692
2024-09-08 17:48:38,919 - train.py[line:541] - INFO: Avg train time: 75.52s, avg eval time: 451.99s, left eval count: 266, ETA: 2024-09-10 11:39:44
2024-09-08 17:49:57,878 - train.py[line:384] - INFO: Epoch 98/500 Iter 100/100: lr=4.9305e-05 loss=0.0853 total_loss=0.1249
2024-09-08 17:49:57,879 - train.py[line:541] - INFO: Avg train time: 76.39s, avg eval time: 451.99s, left eval count: 266, ETA: 2024-09-10 11:45:37
2024-09-08 17:51:22,093 - train.py[line:384] - INFO: Epoch 99/500 Iter 100/100: lr=4.9195e-05 loss=0.1030 total_loss=0.1195
2024-09-08 17:51:22,094 - train.py[line:541] - INFO: Avg train time: 78.99s, avg eval time: 451.99s, left eval count: 266, ETA: 2024-09-10 12:03:08
2024-09-08 17:52:34,971 - train.py[line:384] - INFO: Epoch 100/500 Iter 100/100: lr=4.9084e-05 loss=0.1174 total_loss=0.1212
2024-09-08 18:00:04,496 - engine.py[line:153] - INFO: remove inferior checkpoint: {'epoch': 40, 'metric': 54.38}
2024-09-08 18:00:04,497 - engine.py[line:101] - INFO: Saving checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-100_miou_58.04.pth
2024-09-08 18:00:07,628 - engine.py[line:124] - INFO: Save checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-100_miou_58.04.pth, Time usage:
	prepare checkpoint: 0.010357379913330078, IO: 3.1206343173980713
2024-09-08 18:00:07,628 - train.py[line:525] - INFO: Epoch 100 validation result: mIoU 58.04, best mIoU 58.04
2024-09-08 18:00:07,629 - train.py[line:541] - INFO: Avg train time: 75.94s, avg eval time: 452.26s, left eval count: 265, ETA: 2024-09-10 11:43:53
2024-09-08 18:01:21,792 - train.py[line:384] - INFO: Epoch 101/500 Iter 100/100: lr=4.8974e-05 loss=0.0946 total_loss=0.1199
2024-09-08 18:01:21,793 - train.py[line:541] - INFO: Avg train time: 74.97s, avg eval time: 452.26s, left eval count: 265, ETA: 2024-09-10 11:37:22
2024-09-08 18:02:35,042 - train.py[line:384] - INFO: Epoch 102/500 Iter 100/100: lr=4.8863e-05 loss=0.1248 total_loss=0.1193
2024-09-08 18:02:35,043 - train.py[line:541] - INFO: Avg train time: 73.68s, avg eval time: 452.26s, left eval count: 265, ETA: 2024-09-10 11:28:47
2024-09-08 18:03:48,819 - train.py[line:384] - INFO: Epoch 103/500 Iter 100/100: lr=4.8753e-05 loss=0.1176 total_loss=0.1227
2024-09-08 18:03:48,819 - train.py[line:541] - INFO: Avg train time: 73.22s, avg eval time: 452.26s, left eval count: 265, ETA: 2024-09-10 11:25:43
2024-09-08 18:05:11,639 - train.py[line:384] - INFO: Epoch 104/500 Iter 100/100: lr=4.8642e-05 loss=0.1266 total_loss=0.1172
2024-09-08 18:05:11,640 - train.py[line:541] - INFO: Avg train time: 76.63s, avg eval time: 452.26s, left eval count: 265, ETA: 2024-09-10 11:48:25
2024-09-08 18:06:26,783 - train.py[line:384] - INFO: Epoch 105/500 Iter 100/100: lr=4.8532e-05 loss=0.1181 total_loss=0.1127
2024-09-08 18:06:26,784 - train.py[line:541] - INFO: Avg train time: 75.54s, avg eval time: 452.26s, left eval count: 265, ETA: 2024-09-10 11:41:11
2024-09-08 18:07:37,124 - train.py[line:384] - INFO: Epoch 106/500 Iter 100/100: lr=4.8421e-05 loss=0.1228 total_loss=0.1187
2024-09-08 18:07:37,125 - train.py[line:541] - INFO: Avg train time: 72.85s, avg eval time: 452.26s, left eval count: 265, ETA: 2024-09-10 11:23:28
2024-09-08 18:08:45,325 - train.py[line:384] - INFO: Epoch 107/500 Iter 100/100: lr=4.8310e-05 loss=0.0892 total_loss=0.1166
2024-09-08 18:08:45,325 - train.py[line:541] - INFO: Avg train time: 70.48s, avg eval time: 452.26s, left eval count: 265, ETA: 2024-09-10 11:07:53
2024-09-08 18:10:07,468 - train.py[line:384] - INFO: Epoch 108/500 Iter 100/100: lr=4.8200e-05 loss=0.1272 total_loss=0.1089
2024-09-08 18:10:07,468 - train.py[line:541] - INFO: Avg train time: 74.52s, avg eval time: 452.26s, left eval count: 265, ETA: 2024-09-10 11:34:25
2024-09-08 18:11:24,684 - train.py[line:384] - INFO: Epoch 109/500 Iter 100/100: lr=4.8089e-05 loss=0.0885 total_loss=0.1109
2024-09-08 18:11:24,685 - train.py[line:541] - INFO: Avg train time: 75.07s, avg eval time: 452.26s, left eval count: 265, ETA: 2024-09-10 11:38:05
2024-09-08 18:12:38,430 - train.py[line:384] - INFO: Epoch 110/500 Iter 100/100: lr=4.7978e-05 loss=0.1056 total_loss=0.1075
2024-09-08 18:20:07,910 - train.py[line:525] - INFO: Epoch 110 validation result: mIoU 57.84, best mIoU 58.04
2024-09-08 18:20:07,911 - train.py[line:541] - INFO: Avg train time: 74.09s, avg eval time: 451.15s, left eval count: 264, ETA: 2024-09-10 11:26:45
2024-09-08 18:21:28,252 - train.py[line:384] - INFO: Epoch 111/500 Iter 100/100: lr=4.7868e-05 loss=0.1028 total_loss=0.1041
2024-09-08 18:21:28,253 - train.py[line:541] - INFO: Avg train time: 76.07s, avg eval time: 451.15s, left eval count: 264, ETA: 2024-09-10 11:39:43
2024-09-08 18:22:50,065 - train.py[line:384] - INFO: Epoch 112/500 Iter 100/100: lr=4.7757e-05 loss=0.1230 total_loss=0.1066
2024-09-08 18:22:50,065 - train.py[line:541] - INFO: Avg train time: 77.90s, avg eval time: 451.15s, left eval count: 264, ETA: 2024-09-10 11:51:39
2024-09-08 18:24:14,223 - train.py[line:384] - INFO: Epoch 113/500 Iter 100/100: lr=4.7646e-05 loss=0.2195 total_loss=0.1127
2024-09-08 18:24:14,223 - train.py[line:541] - INFO: Avg train time: 79.91s, avg eval time: 451.15s, left eval count: 264, ETA: 2024-09-10 12:04:41
2024-09-08 18:25:34,824 - train.py[line:384] - INFO: Epoch 114/500 Iter 100/100: lr=4.7535e-05 loss=0.1289 total_loss=0.1070
2024-09-08 18:25:34,824 - train.py[line:541] - INFO: Avg train time: 79.52s, avg eval time: 451.15s, left eval count: 264, ETA: 2024-09-10 12:02:13
2024-09-08 18:26:58,983 - train.py[line:384] - INFO: Epoch 115/500 Iter 100/100: lr=4.7425e-05 loss=0.1389 total_loss=0.1072
2024-09-08 18:26:58,984 - train.py[line:541] - INFO: Avg train time: 80.75s, avg eval time: 451.15s, left eval count: 264, ETA: 2024-09-10 12:10:11
2024-09-08 18:28:22,075 - train.py[line:384] - INFO: Epoch 116/500 Iter 100/100: lr=4.7314e-05 loss=0.0847 total_loss=0.1018
2024-09-08 18:28:22,075 - train.py[line:541] - INFO: Avg train time: 81.23s, avg eval time: 451.15s, left eval count: 264, ETA: 2024-09-10 12:13:18
2024-09-08 18:29:38,007 - train.py[line:384] - INFO: Epoch 117/500 Iter 100/100: lr=4.7203e-05 loss=0.1264 total_loss=0.1069
2024-09-08 18:29:38,008 - train.py[line:541] - INFO: Avg train time: 78.46s, avg eval time: 451.15s, left eval count: 264, ETA: 2024-09-10 11:55:30
2024-09-08 18:31:02,791 - train.py[line:384] - INFO: Epoch 118/500 Iter 100/100: lr=4.7092e-05 loss=0.1060 total_loss=0.1271
2024-09-08 18:31:02,792 - train.py[line:541] - INFO: Avg train time: 80.43s, avg eval time: 451.15s, left eval count: 264, ETA: 2024-09-10 12:08:09
2024-09-08 18:32:26,555 - train.py[line:384] - INFO: Epoch 119/500 Iter 100/100: lr=4.6981e-05 loss=0.0630 total_loss=0.1167
2024-09-08 18:32:26,556 - train.py[line:541] - INFO: Avg train time: 81.15s, avg eval time: 451.15s, left eval count: 264, ETA: 2024-09-10 12:12:49
2024-09-08 18:33:42,136 - train.py[line:384] - INFO: Epoch 120/500 Iter 100/100: lr=4.6870e-05 loss=0.0780 total_loss=0.1054
2024-09-08 18:41:11,750 - engine.py[line:153] - INFO: remove inferior checkpoint: {'epoch': 50, 'metric': 55.05}
2024-09-08 18:41:11,751 - engine.py[line:101] - INFO: Saving checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-120_miou_58.62.pth
2024-09-08 18:41:14,860 - engine.py[line:124] - INFO: Save checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-120_miou_58.62.pth, Time usage:
	prepare checkpoint: 0.011431694030761719, IO: 3.09763765335083
2024-09-08 18:41:14,861 - train.py[line:525] - INFO: Epoch 120 validation result: mIoU 58.62, best mIoU 58.62
2024-09-08 18:41:14,861 - train.py[line:541] - INFO: Avg train time: 78.46s, avg eval time: 451.78s, left eval count: 263, ETA: 2024-09-10 11:58:26
2024-09-08 18:42:32,981 - train.py[line:384] - INFO: Epoch 121/500 Iter 100/100: lr=4.6759e-05 loss=0.0855 total_loss=0.1008
2024-09-08 18:42:32,981 - train.py[line:541] - INFO: Avg train time: 78.04s, avg eval time: 451.78s, left eval count: 263, ETA: 2024-09-10 11:55:48
2024-09-08 18:43:56,935 - train.py[line:384] - INFO: Epoch 122/500 Iter 100/100: lr=4.6648e-05 loss=0.1284 total_loss=0.1008
2024-09-08 18:43:56,936 - train.py[line:541] - INFO: Avg train time: 79.85s, avg eval time: 451.78s, left eval count: 263, ETA: 2024-09-10 12:07:16
2024-09-08 18:45:18,025 - train.py[line:384] - INFO: Epoch 123/500 Iter 100/100: lr=4.6537e-05 loss=0.0819 total_loss=0.0961
2024-09-08 18:45:18,025 - train.py[line:541] - INFO: Avg train time: 79.68s, avg eval time: 451.78s, left eval count: 263, ETA: 2024-09-10 12:06:16
2024-09-08 18:46:26,041 - train.py[line:384] - INFO: Epoch 124/500 Iter 100/100: lr=4.6426e-05 loss=0.0838 total_loss=0.0970
2024-09-08 18:46:26,042 - train.py[line:541] - INFO: Avg train time: 74.48s, avg eval time: 451.78s, left eval count: 263, ETA: 2024-09-10 11:33:26
2024-09-08 18:47:46,868 - train.py[line:384] - INFO: Epoch 125/500 Iter 100/100: lr=4.6314e-05 loss=0.0672 total_loss=0.0937
2024-09-08 18:47:46,869 - train.py[line:541] - INFO: Avg train time: 76.44s, avg eval time: 451.78s, left eval count: 263, ETA: 2024-09-10 11:45:49
2024-09-08 18:49:14,661 - train.py[line:384] - INFO: Epoch 126/500 Iter 100/100: lr=4.6203e-05 loss=0.0729 total_loss=0.0989
2024-09-08 18:49:14,662 - train.py[line:541] - INFO: Avg train time: 80.41s, avg eval time: 451.78s, left eval count: 263, ETA: 2024-09-10 12:10:46
2024-09-08 18:50:21,139 - train.py[line:384] - INFO: Epoch 127/500 Iter 100/100: lr=4.6092e-05 loss=0.1077 total_loss=0.0970
2024-09-08 18:50:21,140 - train.py[line:541] - INFO: Avg train time: 74.43s, avg eval time: 451.78s, left eval count: 263, ETA: 2024-09-10 11:33:20
2024-09-08 18:51:50,151 - train.py[line:384] - INFO: Epoch 128/500 Iter 100/100: lr=4.5981e-05 loss=0.0936 total_loss=0.0886
2024-09-08 18:51:50,151 - train.py[line:541] - INFO: Avg train time: 79.64s, avg eval time: 451.78s, left eval count: 263, ETA: 2024-09-10 12:05:54
2024-09-08 18:53:09,406 - train.py[line:384] - INFO: Epoch 129/500 Iter 100/100: lr=4.5870e-05 loss=0.0868 total_loss=0.0917
2024-09-08 18:53:09,407 - train.py[line:541] - INFO: Avg train time: 78.83s, avg eval time: 451.78s, left eval count: 263, ETA: 2024-09-10 12:00:53
2024-09-08 18:54:36,564 - train.py[line:384] - INFO: Epoch 130/500 Iter 100/100: lr=4.5758e-05 loss=0.0913 total_loss=0.0940
2024-09-08 19:02:06,485 - train.py[line:525] - INFO: Epoch 130 validation result: mIoU 58.28, best mIoU 58.62
2024-09-08 19:02:06,486 - train.py[line:541] - INFO: Avg train time: 81.53s, avg eval time: 451.04s, left eval count: 262, ETA: 2024-09-10 12:14:24
2024-09-08 19:03:29,579 - train.py[line:384] - INFO: Epoch 131/500 Iter 100/100: lr=4.5647e-05 loss=0.0959 total_loss=0.0887
2024-09-08 19:03:29,579 - train.py[line:541] - INFO: Avg train time: 81.71s, avg eval time: 451.04s, left eval count: 262, ETA: 2024-09-10 12:15:31
2024-09-08 19:04:37,637 - train.py[line:384] - INFO: Epoch 132/500 Iter 100/100: lr=4.5536e-05 loss=0.0769 total_loss=0.0910
2024-09-08 19:04:37,637 - train.py[line:541] - INFO: Avg train time: 75.81s, avg eval time: 451.04s, left eval count: 262, ETA: 2024-09-10 11:39:05
2024-09-08 19:05:57,396 - train.py[line:384] - INFO: Epoch 133/500 Iter 100/100: lr=4.5424e-05 loss=0.0903 total_loss=0.0860
2024-09-08 19:05:57,397 - train.py[line:541] - INFO: Avg train time: 76.72s, avg eval time: 451.04s, left eval count: 262, ETA: 2024-09-10 11:44:46
2024-09-08 19:07:20,938 - train.py[line:384] - INFO: Epoch 134/500 Iter 100/100: lr=4.5313e-05 loss=0.1232 total_loss=0.0921
2024-09-08 19:07:20,939 - train.py[line:541] - INFO: Avg train time: 78.97s, avg eval time: 451.04s, left eval count: 262, ETA: 2024-09-10 11:58:36
2024-09-08 19:08:36,341 - train.py[line:384] - INFO: Epoch 135/500 Iter 100/100: lr=4.5201e-05 loss=0.0752 total_loss=0.0891
2024-09-08 19:08:36,341 - train.py[line:541] - INFO: Avg train time: 77.00s, avg eval time: 451.04s, left eval count: 262, ETA: 2024-09-10 11:46:32
2024-09-08 19:09:55,970 - train.py[line:384] - INFO: Epoch 136/500 Iter 100/100: lr=4.5090e-05 loss=0.1102 total_loss=0.0926
2024-09-08 19:09:55,970 - train.py[line:541] - INFO: Avg train time: 77.38s, avg eval time: 451.04s, left eval count: 262, ETA: 2024-09-10 11:48:55
2024-09-08 19:11:17,488 - train.py[line:384] - INFO: Epoch 137/500 Iter 100/100: lr=4.4979e-05 loss=0.0919 total_loss=0.1005
2024-09-08 19:11:17,489 - train.py[line:541] - INFO: Avg train time: 78.50s, avg eval time: 451.04s, left eval count: 262, ETA: 2024-09-10 11:55:44
2024-09-08 19:12:27,581 - train.py[line:384] - INFO: Epoch 138/500 Iter 100/100: lr=4.4867e-05 loss=0.0864 total_loss=0.0956
2024-09-08 19:12:27,582 - train.py[line:541] - INFO: Avg train time: 74.54s, avg eval time: 451.04s, left eval count: 262, ETA: 2024-09-10 11:31:41
2024-09-08 19:13:45,997 - train.py[line:384] - INFO: Epoch 139/500 Iter 100/100: lr=4.4755e-05 loss=0.0913 total_loss=0.0888
2024-09-08 19:13:45,998 - train.py[line:541] - INFO: Avg train time: 75.57s, avg eval time: 451.04s, left eval count: 262, ETA: 2024-09-10 11:37:57
2024-09-08 19:15:06,263 - train.py[line:384] - INFO: Epoch 140/500 Iter 100/100: lr=4.4644e-05 loss=0.0983 total_loss=0.0982
2024-09-08 19:22:36,209 - train.py[line:525] - INFO: Epoch 140 validation result: mIoU 57.8, best mIoU 58.62
2024-09-08 19:22:36,210 - train.py[line:541] - INFO: Avg train time: 76.93s, avg eval time: 450.60s, left eval count: 261, ETA: 2024-09-10 11:44:15
2024-09-08 19:24:01,045 - train.py[line:384] - INFO: Epoch 141/500 Iter 100/100: lr=4.4532e-05 loss=0.0926 total_loss=0.0908
2024-09-08 19:24:01,046 - train.py[line:541] - INFO: Avg train time: 79.62s, avg eval time: 450.60s, left eval count: 261, ETA: 2024-09-10 12:00:31
2024-09-08 19:25:11,624 - train.py[line:384] - INFO: Epoch 142/500 Iter 100/100: lr=4.4421e-05 loss=0.1004 total_loss=0.0872
2024-09-08 19:25:11,625 - train.py[line:541] - INFO: Avg train time: 75.47s, avg eval time: 450.60s, left eval count: 261, ETA: 2024-09-10 11:35:35
2024-09-08 19:26:36,508 - train.py[line:384] - INFO: Epoch 143/500 Iter 100/100: lr=4.4309e-05 loss=0.0800 total_loss=0.0868
2024-09-08 19:26:36,509 - train.py[line:541] - INFO: Avg train time: 78.57s, avg eval time: 450.60s, left eval count: 261, ETA: 2024-09-10 11:54:10
2024-09-08 19:27:50,862 - train.py[line:384] - INFO: Epoch 144/500 Iter 100/100: lr=4.4197e-05 loss=0.1014 total_loss=0.0982
2024-09-08 19:27:50,863 - train.py[line:541] - INFO: Avg train time: 76.37s, avg eval time: 450.60s, left eval count: 261, ETA: 2024-09-10 11:41:04
2024-09-08 19:29:13,436 - train.py[line:384] - INFO: Epoch 145/500 Iter 100/100: lr=4.4085e-05 loss=0.1044 total_loss=0.0873
2024-09-08 19:29:13,437 - train.py[line:541] - INFO: Avg train time: 78.51s, avg eval time: 450.60s, left eval count: 261, ETA: 2024-09-10 11:53:51
2024-09-08 19:30:34,313 - train.py[line:384] - INFO: Epoch 146/500 Iter 100/100: lr=4.3974e-05 loss=0.0806 total_loss=0.0868
2024-09-08 19:30:34,314 - train.py[line:541] - INFO: Avg train time: 79.03s, avg eval time: 450.60s, left eval count: 261, ETA: 2024-09-10 11:56:58
2024-09-08 19:31:46,735 - train.py[line:384] - INFO: Epoch 147/500 Iter 100/100: lr=4.3862e-05 loss=0.0834 total_loss=0.0872
2024-09-08 19:31:46,736 - train.py[line:541] - INFO: Avg train time: 75.92s, avg eval time: 450.60s, left eval count: 261, ETA: 2024-09-10 11:38:32
2024-09-08 19:32:55,352 - train.py[line:384] - INFO: Epoch 148/500 Iter 100/100: lr=4.3750e-05 loss=0.0790 total_loss=0.0855
2024-09-08 19:32:55,353 - train.py[line:541] - INFO: Avg train time: 72.38s, avg eval time: 450.60s, left eval count: 261, ETA: 2024-09-10 11:17:38
2024-09-08 19:34:00,268 - train.py[line:384] - INFO: Epoch 149/500 Iter 100/100: lr=4.3638e-05 loss=0.0792 total_loss=0.0814
2024-09-08 19:34:00,269 - train.py[line:541] - INFO: Avg train time: 68.82s, avg eval time: 450.60s, left eval count: 261, ETA: 2024-09-10 10:56:41
2024-09-08 19:35:16,571 - train.py[line:384] - INFO: Epoch 150/500 Iter 100/100: lr=4.3526e-05 loss=0.0713 total_loss=0.0791
2024-09-08 19:42:44,912 - train.py[line:525] - INFO: Epoch 150 validation result: mIoU 58.53, best mIoU 58.62
2024-09-08 19:42:44,913 - train.py[line:541] - INFO: Avg train time: 71.28s, avg eval time: 449.70s, left eval count: 260, ETA: 2024-09-10 11:07:12
2024-09-08 19:44:06,992 - train.py[line:384] - INFO: Epoch 151/500 Iter 100/100: lr=4.3414e-05 loss=0.0983 total_loss=0.0806
2024-09-08 19:44:06,993 - train.py[line:541] - INFO: Avg train time: 75.04s, avg eval time: 449.70s, left eval count: 260, ETA: 2024-09-10 11:29:16
2024-09-08 19:45:30,448 - train.py[line:384] - INFO: Epoch 152/500 Iter 100/100: lr=4.3302e-05 loss=0.0959 total_loss=0.0803
2024-09-08 19:45:30,449 - train.py[line:541] - INFO: Avg train time: 77.84s, avg eval time: 449.70s, left eval count: 260, ETA: 2024-09-10 11:45:38
2024-09-08 19:46:55,772 - train.py[line:384] - INFO: Epoch 153/500 Iter 100/100: lr=4.3190e-05 loss=0.0686 total_loss=0.0792
2024-09-08 19:46:55,773 - train.py[line:541] - INFO: Avg train time: 80.22s, avg eval time: 449.70s, left eval count: 260, ETA: 2024-09-10 11:59:31
2024-09-08 19:48:07,122 - train.py[line:384] - INFO: Epoch 154/500 Iter 100/100: lr=4.3078e-05 loss=0.0701 total_loss=0.0802
2024-09-08 19:48:07,123 - train.py[line:541] - INFO: Avg train time: 76.26s, avg eval time: 449.70s, left eval count: 260, ETA: 2024-09-10 11:36:33
2024-09-08 19:49:29,275 - train.py[line:384] - INFO: Epoch 155/500 Iter 100/100: lr=4.2966e-05 loss=0.0486 total_loss=0.0800
2024-09-08 19:49:29,276 - train.py[line:541] - INFO: Avg train time: 78.11s, avg eval time: 449.70s, left eval count: 260, ETA: 2024-09-10 11:47:18
2024-09-08 19:50:39,594 - train.py[line:384] - INFO: Epoch 156/500 Iter 100/100: lr=4.2854e-05 loss=0.0921 total_loss=0.0812
2024-09-08 19:50:39,595 - train.py[line:541] - INFO: Avg train time: 74.35s, avg eval time: 449.70s, left eval count: 260, ETA: 2024-09-10 11:25:37
2024-09-08 19:51:54,088 - train.py[line:384] - INFO: Epoch 157/500 Iter 100/100: lr=4.2742e-05 loss=0.0840 total_loss=0.0838
2024-09-08 19:51:54,089 - train.py[line:541] - INFO: Avg train time: 73.93s, avg eval time: 449.70s, left eval count: 260, ETA: 2024-09-10 11:23:11
2024-09-08 19:53:17,995 - train.py[line:384] - INFO: Epoch 158/500 Iter 100/100: lr=4.2630e-05 loss=0.0660 total_loss=0.0798
2024-09-08 19:53:17,995 - train.py[line:541] - INFO: Avg train time: 77.39s, avg eval time: 449.70s, left eval count: 260, ETA: 2024-09-10 11:43:06
2024-09-08 19:54:45,045 - train.py[line:384] - INFO: Epoch 159/500 Iter 100/100: lr=4.2518e-05 loss=0.1044 total_loss=0.0789
2024-09-08 19:54:45,045 - train.py[line:541] - INFO: Avg train time: 80.89s, avg eval time: 449.70s, left eval count: 260, ETA: 2024-09-10 12:03:09
2024-09-08 19:56:04,952 - train.py[line:384] - INFO: Epoch 160/500 Iter 100/100: lr=4.2405e-05 loss=0.0802 total_loss=0.0795
2024-09-08 20:03:34,372 - train.py[line:525] - INFO: Epoch 160 validation result: mIoU 58.47, best mIoU 58.62
2024-09-08 20:03:34,373 - train.py[line:541] - INFO: Avg train time: 79.87s, avg eval time: 449.59s, left eval count: 259, ETA: 2024-09-10 11:56:54
2024-09-08 20:04:41,414 - train.py[line:384] - INFO: Epoch 161/500 Iter 100/100: lr=4.2293e-05 loss=0.0762 total_loss=0.0721
2024-09-08 20:04:41,414 - train.py[line:541] - INFO: Avg train time: 74.33s, avg eval time: 449.59s, left eval count: 259, ETA: 2024-09-10 11:25:22
2024-09-08 20:06:05,866 - train.py[line:384] - INFO: Epoch 162/500 Iter 100/100: lr=4.2181e-05 loss=0.0965 total_loss=0.0733
2024-09-08 20:06:05,866 - train.py[line:541] - INFO: Avg train time: 77.71s, avg eval time: 449.59s, left eval count: 259, ETA: 2024-09-10 11:44:34
2024-09-08 20:07:26,677 - train.py[line:384] - INFO: Epoch 163/500 Iter 100/100: lr=4.2068e-05 loss=0.0820 total_loss=0.0730
2024-09-08 20:07:26,678 - train.py[line:541] - INFO: Avg train time: 78.38s, avg eval time: 449.59s, left eval count: 259, ETA: 2024-09-10 11:48:23
2024-09-08 20:08:48,930 - train.py[line:384] - INFO: Epoch 164/500 Iter 100/100: lr=4.1956e-05 loss=0.0749 total_loss=0.0747
2024-09-08 20:08:48,930 - train.py[line:541] - INFO: Avg train time: 79.28s, avg eval time: 449.59s, left eval count: 259, ETA: 2024-09-10 11:53:29
2024-09-08 20:09:57,661 - train.py[line:384] - INFO: Epoch 165/500 Iter 100/100: lr=4.1844e-05 loss=0.0633 total_loss=0.0770
2024-09-08 20:09:57,661 - train.py[line:541] - INFO: Avg train time: 74.56s, avg eval time: 449.59s, left eval count: 259, ETA: 2024-09-10 11:26:56
2024-09-08 20:11:08,647 - train.py[line:384] - INFO: Epoch 166/500 Iter 100/100: lr=4.1731e-05 loss=0.0978 total_loss=0.0719
2024-09-08 20:11:08,648 - train.py[line:541] - INFO: Avg train time: 72.59s, avg eval time: 449.59s, left eval count: 259, ETA: 2024-09-10 11:15:57
2024-09-08 20:12:24,016 - train.py[line:384] - INFO: Epoch 167/500 Iter 100/100: lr=4.1619e-05 loss=0.0923 total_loss=0.0722
2024-09-08 20:12:24,016 - train.py[line:541] - INFO: Avg train time: 73.05s, avg eval time: 449.59s, left eval count: 259, ETA: 2024-09-10 11:18:32
2024-09-08 20:13:47,559 - train.py[line:384] - INFO: Epoch 168/500 Iter 100/100: lr=4.1506e-05 loss=0.0941 total_loss=0.0725
2024-09-08 20:13:47,560 - train.py[line:541] - INFO: Avg train time: 76.58s, avg eval time: 449.59s, left eval count: 259, ETA: 2024-09-10 11:38:14
2024-09-08 20:15:12,978 - train.py[line:384] - INFO: Epoch 169/500 Iter 100/100: lr=4.1394e-05 loss=0.0784 total_loss=0.0738
2024-09-08 20:15:12,978 - train.py[line:541] - INFO: Avg train time: 79.55s, avg eval time: 449.59s, left eval count: 259, ETA: 2024-09-10 11:54:45
2024-09-08 20:16:29,289 - train.py[line:384] - INFO: Epoch 170/500 Iter 100/100: lr=4.1281e-05 loss=0.0715 total_loss=0.0794
2024-09-08 20:23:58,760 - train.py[line:525] - INFO: Epoch 170 validation result: mIoU 57.86, best mIoU 58.62
2024-09-08 20:23:58,761 - train.py[line:541] - INFO: Avg train time: 77.68s, avg eval time: 449.54s, left eval count: 258, ETA: 2024-09-10 11:44:13
2024-09-08 20:25:17,573 - train.py[line:384] - INFO: Epoch 171/500 Iter 100/100: lr=4.1169e-05 loss=0.0753 total_loss=0.0718
2024-09-08 20:25:17,574 - train.py[line:541] - INFO: Avg train time: 77.64s, avg eval time: 449.54s, left eval count: 258, ETA: 2024-09-10 11:44:02
2024-09-08 20:26:42,997 - train.py[line:384] - INFO: Epoch 172/500 Iter 100/100: lr=4.1056e-05 loss=0.0758 total_loss=0.0685
2024-09-08 20:26:42,999 - train.py[line:541] - INFO: Avg train time: 80.26s, avg eval time: 449.54s, left eval count: 258, ETA: 2024-09-10 11:58:30
2024-09-08 20:27:55,826 - train.py[line:384] - INFO: Epoch 173/500 Iter 100/100: lr=4.0943e-05 loss=0.0914 total_loss=0.0681
2024-09-08 20:27:55,826 - train.py[line:541] - INFO: Avg train time: 76.56s, avg eval time: 449.54s, left eval count: 258, ETA: 2024-09-10 11:38:10
2024-09-08 20:29:14,944 - train.py[line:384] - INFO: Epoch 174/500 Iter 100/100: lr=4.0831e-05 loss=0.0680 total_loss=0.0692
2024-09-08 20:29:14,945 - train.py[line:541] - INFO: Avg train time: 77.03s, avg eval time: 449.54s, left eval count: 258, ETA: 2024-09-10 11:40:49
2024-09-08 20:30:33,342 - train.py[line:384] - INFO: Epoch 175/500 Iter 100/100: lr=4.0718e-05 loss=0.0900 total_loss=0.0694
2024-09-08 20:30:33,343 - train.py[line:541] - INFO: Avg train time: 76.92s, avg eval time: 449.54s, left eval count: 258, ETA: 2024-09-10 11:40:13
2024-09-08 20:31:52,569 - train.py[line:384] - INFO: Epoch 176/500 Iter 100/100: lr=4.0605e-05 loss=0.0522 total_loss=0.0668
2024-09-08 20:31:52,569 - train.py[line:541] - INFO: Avg train time: 77.26s, avg eval time: 449.54s, left eval count: 258, ETA: 2024-09-10 11:42:07
2024-09-08 20:33:15,975 - train.py[line:384] - INFO: Epoch 177/500 Iter 100/100: lr=4.0492e-05 loss=0.0620 total_loss=0.0707
2024-09-08 20:33:15,975 - train.py[line:541] - INFO: Avg train time: 79.23s, avg eval time: 449.54s, left eval count: 258, ETA: 2024-09-10 11:52:49
2024-09-08 20:34:33,364 - train.py[line:384] - INFO: Epoch 178/500 Iter 100/100: lr=4.0379e-05 loss=0.0539 total_loss=0.0674
2024-09-08 20:34:33,364 - train.py[line:541] - INFO: Avg train time: 78.08s, avg eval time: 449.54s, left eval count: 258, ETA: 2024-09-10 11:46:36
2024-09-08 20:35:57,426 - train.py[line:384] - INFO: Epoch 179/500 Iter 100/100: lr=4.0267e-05 loss=0.0619 total_loss=0.0681
2024-09-08 20:35:57,426 - train.py[line:541] - INFO: Avg train time: 79.81s, avg eval time: 449.54s, left eval count: 258, ETA: 2024-09-10 11:55:58
2024-09-08 20:37:11,221 - train.py[line:384] - INFO: Epoch 180/500 Iter 100/100: lr=4.0154e-05 loss=0.1030 total_loss=0.0657
2024-09-08 20:44:40,695 - train.py[line:525] - INFO: Epoch 180 validation result: mIoU 58.24, best mIoU 58.62
2024-09-08 20:44:40,695 - train.py[line:541] - INFO: Avg train time: 77.01s, avg eval time: 449.51s, left eval count: 257, ETA: 2024-09-10 11:40:49
2024-09-08 20:45:59,924 - train.py[line:384] - INFO: Epoch 181/500 Iter 100/100: lr=4.0041e-05 loss=0.0499 total_loss=0.0683
2024-09-08 20:45:59,925 - train.py[line:541] - INFO: Avg train time: 77.46s, avg eval time: 449.51s, left eval count: 257, ETA: 2024-09-10 11:43:14
2024-09-08 20:47:24,952 - train.py[line:384] - INFO: Epoch 182/500 Iter 100/100: lr=3.9928e-05 loss=0.0948 total_loss=0.0691
2024-09-08 20:47:24,952 - train.py[line:541] - INFO: Avg train time: 80.10s, avg eval time: 449.51s, left eval count: 257, ETA: 2024-09-10 11:57:20
2024-09-08 20:48:54,953 - train.py[line:384] - INFO: Epoch 183/500 Iter 100/100: lr=3.9815e-05 loss=0.0799 total_loss=0.0678
2024-09-08 20:48:54,955 - train.py[line:541] - INFO: Avg train time: 83.44s, avg eval time: 449.51s, left eval count: 257, ETA: 2024-09-10 12:15:10
2024-09-08 20:50:08,522 - train.py[line:384] - INFO: Epoch 184/500 Iter 100/100: lr=3.9702e-05 loss=0.0711 total_loss=0.0651
2024-09-08 20:50:08,523 - train.py[line:541] - INFO: Avg train time: 78.91s, avg eval time: 449.51s, left eval count: 257, ETA: 2024-09-10 11:51:10
2024-09-08 20:51:30,381 - train.py[line:384] - INFO: Epoch 185/500 Iter 100/100: lr=3.9589e-05 loss=0.0679 total_loss=0.0660
2024-09-08 20:51:30,381 - train.py[line:541] - INFO: Avg train time: 79.51s, avg eval time: 449.51s, left eval count: 257, ETA: 2024-09-10 11:54:19
2024-09-08 20:52:53,265 - train.py[line:384] - INFO: Epoch 186/500 Iter 100/100: lr=3.9475e-05 loss=0.0539 total_loss=0.0649
2024-09-08 20:52:53,266 - train.py[line:541] - INFO: Avg train time: 80.37s, avg eval time: 449.51s, left eval count: 257, ETA: 2024-09-10 11:58:54
2024-09-08 20:54:06,733 - train.py[line:384] - INFO: Epoch 187/500 Iter 100/100: lr=3.9362e-05 loss=0.0572 total_loss=0.0656
2024-09-08 20:54:06,733 - train.py[line:541] - INFO: Avg train time: 77.00s, avg eval time: 449.51s, left eval count: 257, ETA: 2024-09-10 11:41:13
2024-09-08 20:55:28,302 - train.py[line:384] - INFO: Epoch 188/500 Iter 100/100: lr=3.9249e-05 loss=0.0629 total_loss=0.0619
2024-09-08 20:55:28,303 - train.py[line:541] - INFO: Avg train time: 78.29s, avg eval time: 449.51s, left eval count: 257, ETA: 2024-09-10 11:47:59
2024-09-08 20:56:41,190 - train.py[line:384] - INFO: Epoch 189/500 Iter 100/100: lr=3.9136e-05 loss=0.0768 total_loss=0.0667
2024-09-08 20:56:41,190 - train.py[line:541] - INFO: Avg train time: 75.73s, avg eval time: 449.51s, left eval count: 257, ETA: 2024-09-10 11:34:36
2024-09-08 20:58:05,966 - train.py[line:384] - INFO: Epoch 190/500 Iter 100/100: lr=3.9023e-05 loss=0.0587 total_loss=0.0640
2024-09-08 21:05:35,643 - train.py[line:525] - INFO: Epoch 190 validation result: mIoU 58.36, best mIoU 58.62
2024-09-08 21:05:35,644 - train.py[line:541] - INFO: Avg train time: 78.70s, avg eval time: 449.58s, left eval count: 256, ETA: 2024-09-10 11:50:24
2024-09-08 21:06:56,869 - train.py[line:384] - INFO: Epoch 191/500 Iter 100/100: lr=3.8909e-05 loss=0.0622 total_loss=0.0629
2024-09-08 21:06:56,869 - train.py[line:541] - INFO: Avg train time: 79.17s, avg eval time: 449.58s, left eval count: 256, ETA: 2024-09-10 11:52:51
2024-09-08 21:08:13,058 - train.py[line:384] - INFO: Epoch 192/500 Iter 100/100: lr=3.8796e-05 loss=0.0631 total_loss=0.0650
2024-09-08 21:08:13,059 - train.py[line:541] - INFO: Avg train time: 77.29s, avg eval time: 449.58s, left eval count: 256, ETA: 2024-09-10 11:43:09
2024-09-08 21:09:30,616 - train.py[line:384] - INFO: Epoch 193/500 Iter 100/100: lr=3.8683e-05 loss=0.0976 total_loss=0.0715
2024-09-08 21:09:30,616 - train.py[line:541] - INFO: Avg train time: 76.82s, avg eval time: 449.58s, left eval count: 256, ETA: 2024-09-10 11:40:47
2024-09-08 21:10:58,087 - train.py[line:384] - INFO: Epoch 194/500 Iter 100/100: lr=3.8569e-05 loss=0.1027 total_loss=0.0634
2024-09-08 21:10:58,088 - train.py[line:541] - INFO: Avg train time: 80.58s, avg eval time: 449.58s, left eval count: 256, ETA: 2024-09-10 12:00:07
2024-09-08 21:12:07,875 - train.py[line:384] - INFO: Epoch 195/500 Iter 100/100: lr=3.8456e-05 loss=0.0678 total_loss=0.0626
2024-09-08 21:12:07,875 - train.py[line:541] - INFO: Avg train time: 75.62s, avg eval time: 449.58s, left eval count: 256, ETA: 2024-09-10 11:34:45
2024-09-08 21:13:17,248 - train.py[line:384] - INFO: Epoch 196/500 Iter 100/100: lr=3.8342e-05 loss=0.0754 total_loss=0.0640
2024-09-08 21:13:17,249 - train.py[line:541] - INFO: Avg train time: 72.48s, avg eval time: 449.58s, left eval count: 256, ETA: 2024-09-10 11:18:43
2024-09-08 21:14:44,580 - train.py[line:384] - INFO: Epoch 197/500 Iter 100/100: lr=3.8229e-05 loss=0.1943 total_loss=0.0618
2024-09-08 21:14:44,581 - train.py[line:541] - INFO: Avg train time: 77.96s, avg eval time: 449.58s, left eval count: 256, ETA: 2024-09-10 11:46:37
2024-09-08 21:16:09,810 - train.py[line:384] - INFO: Epoch 198/500 Iter 100/100: lr=3.8115e-05 loss=0.0642 total_loss=0.0687
2024-09-08 21:16:09,810 - train.py[line:541] - INFO: Avg train time: 80.22s, avg eval time: 449.58s, left eval count: 256, ETA: 2024-09-10 11:58:08
2024-09-08 21:17:34,557 - train.py[line:384] - INFO: Epoch 199/500 Iter 100/100: lr=3.8002e-05 loss=0.0580 total_loss=0.0672
2024-09-08 21:17:34,558 - train.py[line:541] - INFO: Avg train time: 81.36s, avg eval time: 449.58s, left eval count: 256, ETA: 2024-09-10 12:03:57
2024-09-08 21:19:00,834 - train.py[line:384] - INFO: Epoch 200/500 Iter 100/100: lr=3.7888e-05 loss=0.0523 total_loss=0.0640
2024-09-08 21:26:29,947 - train.py[line:525] - INFO: Epoch 200 validation result: mIoU 57.9, best mIoU 58.62
2024-09-08 21:26:29,948 - train.py[line:541] - INFO: Avg train time: 82.76s, avg eval time: 449.39s, left eval count: 255, ETA: 2024-09-10 12:10:11
2024-09-08 21:27:52,003 - train.py[line:384] - INFO: Epoch 201/500 Iter 100/100: lr=3.7774e-05 loss=0.0502 total_loss=0.0613
2024-09-08 21:27:52,004 - train.py[line:541] - INFO: Avg train time: 82.01s, avg eval time: 449.39s, left eval count: 255, ETA: 2024-09-10 12:06:28
2024-09-08 21:29:16,085 - train.py[line:384] - INFO: Epoch 202/500 Iter 100/100: lr=3.7660e-05 loss=0.1373 total_loss=0.0604
2024-09-08 21:29:16,086 - train.py[line:541] - INFO: Avg train time: 82.20s, avg eval time: 449.39s, left eval count: 255, ETA: 2024-09-10 12:07:25
2024-09-08 21:30:29,373 - train.py[line:384] - INFO: Epoch 203/500 Iter 100/100: lr=3.7547e-05 loss=0.0826 total_loss=0.0621
2024-09-08 21:30:29,373 - train.py[line:541] - INFO: Avg train time: 78.07s, avg eval time: 449.39s, left eval count: 255, ETA: 2024-09-10 11:46:52
2024-09-08 21:31:39,575 - train.py[line:384] - INFO: Epoch 204/500 Iter 100/100: lr=3.7433e-05 loss=0.0577 total_loss=0.0616
2024-09-08 21:31:39,576 - train.py[line:541] - INFO: Avg train time: 74.23s, avg eval time: 449.39s, left eval count: 255, ETA: 2024-09-10 11:27:48
2024-09-08 21:32:49,108 - train.py[line:384] - INFO: Epoch 205/500 Iter 100/100: lr=3.7319e-05 loss=0.0711 total_loss=0.0600
2024-09-08 21:32:49,109 - train.py[line:541] - INFO: Avg train time: 71.78s, avg eval time: 449.39s, left eval count: 255, ETA: 2024-09-10 11:15:40
2024-09-08 21:33:58,028 - train.py[line:384] - INFO: Epoch 206/500 Iter 100/100: lr=3.7205e-05 loss=0.0566 total_loss=0.0600
2024-09-08 21:33:58,029 - train.py[line:541] - INFO: Avg train time: 70.01s, avg eval time: 449.39s, left eval count: 255, ETA: 2024-09-10 11:06:55
2024-09-08 21:35:14,587 - train.py[line:384] - INFO: Epoch 207/500 Iter 100/100: lr=3.7091e-05 loss=0.0766 total_loss=0.0591
2024-09-08 21:35:14,587 - train.py[line:541] - INFO: Avg train time: 72.01s, avg eval time: 449.39s, left eval count: 255, ETA: 2024-09-10 11:16:47
2024-09-08 21:36:39,365 - train.py[line:384] - INFO: Epoch 208/500 Iter 100/100: lr=3.6977e-05 loss=0.0890 total_loss=0.0570
2024-09-08 21:36:39,366 - train.py[line:541] - INFO: Avg train time: 76.60s, avg eval time: 449.39s, left eval count: 255, ETA: 2024-09-10 11:39:20
2024-09-08 21:37:54,185 - train.py[line:384] - INFO: Epoch 209/500 Iter 100/100: lr=3.6863e-05 loss=0.0587 total_loss=0.0569
2024-09-08 21:37:54,186 - train.py[line:541] - INFO: Avg train time: 75.31s, avg eval time: 449.39s, left eval count: 255, ETA: 2024-09-10 11:33:05
2024-09-08 21:39:21,968 - train.py[line:384] - INFO: Epoch 210/500 Iter 100/100: lr=3.6749e-05 loss=0.0743 total_loss=0.0581
2024-09-08 21:46:51,257 - engine.py[line:153] - INFO: remove inferior checkpoint: {'epoch': 60, 'metric': 56.07}
2024-09-08 21:46:51,258 - engine.py[line:101] - INFO: Saving checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-210_miou_58.73.pth
2024-09-08 21:46:54,398 - engine.py[line:124] - INFO: Save checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-210_miou_58.73.pth, Time usage:
	prepare checkpoint: 0.01022648811340332, IO: 3.1301536560058594
2024-09-08 21:46:54,399 - train.py[line:525] - INFO: Epoch 210 validation result: mIoU 58.73, best mIoU 58.73
2024-09-08 21:46:54,399 - train.py[line:541] - INFO: Avg train time: 79.77s, avg eval time: 450.61s, left eval count: 254, ETA: 2024-09-10 12:00:02
2024-09-08 21:48:16,812 - train.py[line:384] - INFO: Epoch 211/500 Iter 100/100: lr=3.6635e-05 loss=0.0591 total_loss=0.0545
2024-09-08 21:48:16,812 - train.py[line:541] - INFO: Avg train time: 80.53s, avg eval time: 450.61s, left eval count: 254, ETA: 2024-09-10 12:03:43
2024-09-08 21:49:44,736 - train.py[line:384] - INFO: Epoch 212/500 Iter 100/100: lr=3.6521e-05 loss=0.0666 total_loss=0.0577
2024-09-08 21:49:44,736 - train.py[line:541] - INFO: Avg train time: 82.93s, avg eval time: 450.61s, left eval count: 254, ETA: 2024-09-10 12:15:22
2024-09-08 21:51:13,410 - train.py[line:384] - INFO: Epoch 213/500 Iter 100/100: lr=3.6407e-05 loss=0.0602 total_loss=0.0590
2024-09-08 21:51:13,411 - train.py[line:541] - INFO: Avg train time: 84.65s, avg eval time: 450.61s, left eval count: 254, ETA: 2024-09-10 12:23:41
2024-09-08 21:52:28,986 - train.py[line:384] - INFO: Epoch 214/500 Iter 100/100: lr=3.6293e-05 loss=0.0392 total_loss=0.0583
2024-09-08 21:52:28,986 - train.py[line:541] - INFO: Avg train time: 80.43s, avg eval time: 450.61s, left eval count: 254, ETA: 2024-09-10 12:03:25
2024-09-08 21:53:40,186 - train.py[line:384] - INFO: Epoch 215/500 Iter 100/100: lr=3.6179e-05 loss=0.0383 total_loss=0.0588
2024-09-08 21:53:40,187 - train.py[line:541] - INFO: Avg train time: 76.13s, avg eval time: 450.61s, left eval count: 254, ETA: 2024-09-10 11:42:50
2024-09-08 21:55:06,957 - train.py[line:384] - INFO: Epoch 216/500 Iter 100/100: lr=3.6064e-05 loss=0.0562 total_loss=0.0569
2024-09-08 21:55:06,958 - train.py[line:541] - INFO: Avg train time: 79.73s, avg eval time: 450.61s, left eval count: 254, ETA: 2024-09-10 12:00:05
2024-09-08 21:56:25,437 - train.py[line:384] - INFO: Epoch 217/500 Iter 100/100: lr=3.5950e-05 loss=0.0415 total_loss=0.0534
2024-09-08 21:56:25,438 - train.py[line:541] - INFO: Avg train time: 78.74s, avg eval time: 450.61s, left eval count: 254, ETA: 2024-09-10 11:55:23
2024-09-08 21:57:40,532 - train.py[line:384] - INFO: Epoch 218/500 Iter 100/100: lr=3.5836e-05 loss=0.0485 total_loss=0.0552
2024-09-08 21:57:40,533 - train.py[line:541] - INFO: Avg train time: 76.82s, avg eval time: 450.61s, left eval count: 254, ETA: 2024-09-10 11:46:18
2024-09-08 21:59:06,223 - train.py[line:384] - INFO: Epoch 219/500 Iter 100/100: lr=3.5721e-05 loss=0.0491 total_loss=0.0537
2024-09-08 21:59:06,224 - train.py[line:541] - INFO: Avg train time: 79.83s, avg eval time: 450.61s, left eval count: 254, ETA: 2024-09-10 12:00:31
2024-09-08 22:00:33,896 - train.py[line:384] - INFO: Epoch 220/500 Iter 100/100: lr=3.5607e-05 loss=0.0761 total_loss=0.0556
2024-09-08 22:08:03,371 - train.py[line:525] - INFO: Epoch 220 validation result: mIoU 58.65, best mIoU 58.73
2024-09-08 22:08:03,372 - train.py[line:541] - INFO: Avg train time: 82.26s, avg eval time: 450.15s, left eval count: 253, ETA: 2024-09-10 12:10:05
2024-09-08 22:09:19,842 - train.py[line:384] - INFO: Epoch 221/500 Iter 100/100: lr=3.5492e-05 loss=0.0863 total_loss=0.0560
2024-09-08 22:09:19,843 - train.py[line:541] - INFO: Avg train time: 79.38s, avg eval time: 450.15s, left eval count: 253, ETA: 2024-09-10 11:56:35
2024-09-08 22:10:47,828 - train.py[line:384] - INFO: Epoch 222/500 Iter 100/100: lr=3.5378e-05 loss=0.0634 total_loss=0.0538
2024-09-08 22:10:47,828 - train.py[line:541] - INFO: Avg train time: 82.16s, avg eval time: 450.15s, left eval count: 253, ETA: 2024-09-10 12:09:37
2024-09-08 22:12:11,456 - train.py[line:384] - INFO: Epoch 223/500 Iter 100/100: lr=3.5263e-05 loss=0.0727 total_loss=0.0546
2024-09-08 22:12:11,456 - train.py[line:541] - INFO: Avg train time: 82.16s, avg eval time: 450.15s, left eval count: 253, ETA: 2024-09-10 12:09:38
2024-09-08 22:13:32,277 - train.py[line:384] - INFO: Epoch 224/500 Iter 100/100: lr=3.5149e-05 loss=0.0504 total_loss=0.0534
2024-09-08 22:13:32,278 - train.py[line:541] - INFO: Avg train time: 81.04s, avg eval time: 450.15s, left eval count: 253, ETA: 2024-09-10 12:04:29
2024-09-08 22:14:58,249 - train.py[line:384] - INFO: Epoch 225/500 Iter 100/100: lr=3.5034e-05 loss=0.0577 total_loss=0.0539
2024-09-08 22:14:58,250 - train.py[line:541] - INFO: Avg train time: 82.52s, avg eval time: 450.15s, left eval count: 253, ETA: 2024-09-10 12:11:21
2024-09-08 22:16:07,461 - train.py[line:384] - INFO: Epoch 226/500 Iter 100/100: lr=3.4920e-05 loss=0.0646 total_loss=0.0584
2024-09-08 22:16:07,461 - train.py[line:541] - INFO: Avg train time: 76.61s, avg eval time: 450.15s, left eval count: 253, ETA: 2024-09-10 11:44:08
2024-09-08 22:17:31,024 - train.py[line:384] - INFO: Epoch 227/500 Iter 100/100: lr=3.4805e-05 loss=0.0472 total_loss=0.0543
2024-09-08 22:17:31,025 - train.py[line:541] - INFO: Avg train time: 78.86s, avg eval time: 450.15s, left eval count: 253, ETA: 2024-09-10 11:54:30
2024-09-08 22:18:43,288 - train.py[line:384] - INFO: Epoch 228/500 Iter 100/100: lr=3.4690e-05 loss=0.0411 total_loss=0.0531
2024-09-08 22:18:43,289 - train.py[line:541] - INFO: Avg train time: 75.84s, avg eval time: 450.15s, left eval count: 253, ETA: 2024-09-10 11:40:41
2024-09-08 22:20:12,935 - train.py[line:384] - INFO: Epoch 229/500 Iter 100/100: lr=3.4575e-05 loss=0.0494 total_loss=0.0510
2024-09-08 22:20:12,936 - train.py[line:541] - INFO: Avg train time: 80.77s, avg eval time: 450.15s, left eval count: 253, ETA: 2024-09-10 12:03:09
2024-09-08 22:21:28,356 - train.py[line:384] - INFO: Epoch 230/500 Iter 100/100: lr=3.4460e-05 loss=0.0616 total_loss=0.0552
2024-09-08 22:28:57,382 - train.py[line:525] - INFO: Epoch 230 validation result: mIoU 58.56, best mIoU 58.73
2024-09-08 22:28:57,383 - train.py[line:541] - INFO: Avg train time: 78.15s, avg eval time: 449.70s, left eval count: 252, ETA: 2024-09-10 11:49:23
2024-09-08 22:30:10,017 - train.py[line:384] - INFO: Epoch 231/500 Iter 100/100: lr=3.4345e-05 loss=0.0455 total_loss=0.0600
2024-09-08 22:30:10,017 - train.py[line:541] - INFO: Avg train time: 75.38s, avg eval time: 449.70s, left eval count: 252, ETA: 2024-09-10 11:36:53
2024-09-08 22:31:31,761 - train.py[line:384] - INFO: Epoch 232/500 Iter 100/100: lr=3.4231e-05 loss=0.0681 total_loss=0.0553
2024-09-08 22:31:31,762 - train.py[line:541] - INFO: Avg train time: 77.34s, avg eval time: 449.70s, left eval count: 252, ETA: 2024-09-10 11:45:44
2024-09-08 22:32:46,618 - train.py[line:384] - INFO: Epoch 233/500 Iter 100/100: lr=3.4116e-05 loss=0.0594 total_loss=0.0534
2024-09-08 22:32:46,619 - train.py[line:541] - INFO: Avg train time: 75.77s, avg eval time: 449.70s, left eval count: 252, ETA: 2024-09-10 11:38:42
2024-09-08 22:33:56,384 - train.py[line:384] - INFO: Epoch 234/500 Iter 100/100: lr=3.4001e-05 loss=0.0544 total_loss=0.0530
2024-09-08 22:33:56,385 - train.py[line:541] - INFO: Avg train time: 72.70s, avg eval time: 449.70s, left eval count: 252, ETA: 2024-09-10 11:24:59
2024-09-08 22:35:14,465 - train.py[line:384] - INFO: Epoch 235/500 Iter 100/100: lr=3.3886e-05 loss=0.0545 total_loss=0.0525
2024-09-08 22:35:14,465 - train.py[line:541] - INFO: Avg train time: 74.17s, avg eval time: 449.70s, left eval count: 252, ETA: 2024-09-10 11:31:33
2024-09-08 22:36:30,057 - train.py[line:384] - INFO: Epoch 236/500 Iter 100/100: lr=3.3770e-05 loss=0.0465 total_loss=0.0514
2024-09-08 22:36:30,058 - train.py[line:541] - INFO: Avg train time: 74.20s, avg eval time: 449.70s, left eval count: 252, ETA: 2024-09-10 11:31:43
2024-09-08 22:37:56,174 - train.py[line:384] - INFO: Epoch 237/500 Iter 100/100: lr=3.3655e-05 loss=0.0494 total_loss=0.0518
2024-09-08 22:37:56,174 - train.py[line:541] - INFO: Avg train time: 78.31s, avg eval time: 449.70s, left eval count: 252, ETA: 2024-09-10 11:49:56
2024-09-08 22:39:10,857 - train.py[line:384] - INFO: Epoch 238/500 Iter 100/100: lr=3.3540e-05 loss=0.0577 total_loss=0.0509
2024-09-08 22:39:10,857 - train.py[line:541] - INFO: Avg train time: 76.41s, avg eval time: 449.70s, left eval count: 252, ETA: 2024-09-10 11:41:35
2024-09-08 22:40:28,795 - train.py[line:384] - INFO: Epoch 239/500 Iter 100/100: lr=3.3425e-05 loss=0.0546 total_loss=0.0519
2024-09-08 22:40:28,796 - train.py[line:541] - INFO: Avg train time: 76.45s, avg eval time: 449.70s, left eval count: 252, ETA: 2024-09-10 11:41:46
2024-09-08 22:41:41,049 - train.py[line:384] - INFO: Epoch 240/500 Iter 100/100: lr=3.3310e-05 loss=0.0375 total_loss=0.0547
2024-09-08 22:49:10,585 - train.py[line:525] - INFO: Epoch 240 validation result: mIoU 58.11, best mIoU 58.73
2024-09-08 22:49:10,586 - train.py[line:541] - INFO: Avg train time: 74.16s, avg eval time: 449.64s, left eval count: 251, ETA: 2024-09-10 11:31:31
2024-09-08 22:50:33,984 - train.py[line:384] - INFO: Epoch 241/500 Iter 100/100: lr=3.3194e-05 loss=0.0407 total_loss=0.0519
2024-09-08 22:50:33,984 - train.py[line:541] - INFO: Avg train time: 77.44s, avg eval time: 449.64s, left eval count: 251, ETA: 2024-09-10 11:45:50
2024-09-08 22:51:50,438 - train.py[line:384] - INFO: Epoch 242/500 Iter 100/100: lr=3.3079e-05 loss=0.0463 total_loss=0.0515
2024-09-08 22:51:50,439 - train.py[line:541] - INFO: Avg train time: 76.45s, avg eval time: 449.64s, left eval count: 251, ETA: 2024-09-10 11:41:33
2024-09-08 22:53:04,464 - train.py[line:384] - INFO: Epoch 243/500 Iter 100/100: lr=3.2963e-05 loss=0.0686 total_loss=0.0569
2024-09-08 22:53:04,465 - train.py[line:541] - INFO: Avg train time: 74.97s, avg eval time: 449.64s, left eval count: 251, ETA: 2024-09-10 11:35:11
2024-09-08 22:54:19,328 - train.py[line:384] - INFO: Epoch 244/500 Iter 100/100: lr=3.2848e-05 loss=0.0678 total_loss=0.0576
2024-09-08 22:54:19,329 - train.py[line:541] - INFO: Avg train time: 74.28s, avg eval time: 449.64s, left eval count: 251, ETA: 2024-09-10 11:32:13
2024-09-08 22:55:43,245 - train.py[line:384] - INFO: Epoch 245/500 Iter 100/100: lr=3.2733e-05 loss=0.0535 total_loss=0.0517
2024-09-08 22:55:43,246 - train.py[line:541] - INFO: Avg train time: 77.47s, avg eval time: 449.64s, left eval count: 251, ETA: 2024-09-10 11:45:56
2024-09-08 22:57:07,585 - train.py[line:384] - INFO: Epoch 246/500 Iter 100/100: lr=3.2617e-05 loss=0.0554 total_loss=0.0511
2024-09-08 22:57:07,586 - train.py[line:541] - INFO: Avg train time: 79.63s, avg eval time: 449.64s, left eval count: 251, ETA: 2024-09-10 11:55:12
2024-09-08 22:58:36,345 - train.py[line:384] - INFO: Epoch 247/500 Iter 100/100: lr=3.2501e-05 loss=0.0468 total_loss=0.0494
2024-09-08 22:58:36,346 - train.py[line:541] - INFO: Avg train time: 82.67s, avg eval time: 449.64s, left eval count: 251, ETA: 2024-09-10 12:08:10
2024-09-08 22:59:58,441 - train.py[line:384] - INFO: Epoch 248/500 Iter 100/100: lr=3.2386e-05 loss=0.0506 total_loss=0.0487
2024-09-08 22:59:58,442 - train.py[line:541] - INFO: Avg train time: 81.82s, avg eval time: 449.64s, left eval count: 251, ETA: 2024-09-10 12:04:35
2024-09-08 23:01:13,389 - train.py[line:384] - INFO: Epoch 249/500 Iter 100/100: lr=3.2270e-05 loss=0.0683 total_loss=0.0487
2024-09-08 23:01:13,389 - train.py[line:541] - INFO: Avg train time: 78.52s, avg eval time: 449.64s, left eval count: 251, ETA: 2024-09-10 11:50:41
2024-09-08 23:02:39,282 - train.py[line:384] - INFO: Epoch 250/500 Iter 100/100: lr=3.2154e-05 loss=0.0232 total_loss=0.0485
2024-09-08 23:10:08,129 - engine.py[line:153] - INFO: remove inferior checkpoint: {'epoch': 80, 'metric': 57.1}
2024-09-08 23:10:08,129 - engine.py[line:101] - INFO: Saving checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-250_miou_58.92.pth
2024-09-08 23:10:12,274 - engine.py[line:124] - INFO: Save checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-250_miou_58.92.pth, Time usage:
	prepare checkpoint: 0.010309934616088867, IO: 4.134366273880005
2024-09-08 23:10:12,275 - train.py[line:525] - INFO: Epoch 250 validation result: mIoU 58.92, best mIoU 58.92
2024-09-08 23:10:12,275 - train.py[line:541] - INFO: Avg train time: 80.87s, avg eval time: 450.98s, left eval count: 250, ETA: 2024-09-10 12:06:15
2024-09-08 23:11:29,811 - train.py[line:384] - INFO: Epoch 251/500 Iter 100/100: lr=3.2039e-05 loss=0.0611 total_loss=0.0502
2024-09-08 23:18:59,808 - train.py[line:525] - INFO: Epoch 251 validation result: mIoU 58.11, best mIoU 58.92
2024-09-08 23:18:59,809 - train.py[line:541] - INFO: Avg train time: 79.29s, avg eval time: 450.59s, left eval count: 249, ETA: 2024-09-10 11:57:57
2024-09-08 23:20:23,482 - train.py[line:384] - INFO: Epoch 252/500 Iter 100/100: lr=3.1923e-05 loss=0.0507 total_loss=0.0483
2024-09-08 23:27:53,879 - train.py[line:525] - INFO: Epoch 252 validation result: mIoU 58.51, best mIoU 58.92
2024-09-08 23:27:53,880 - train.py[line:541] - INFO: Avg train time: 80.53s, avg eval time: 450.51s, left eval count: 248, ETA: 2024-09-10 12:02:52
2024-09-08 23:29:11,637 - train.py[line:384] - INFO: Epoch 253/500 Iter 100/100: lr=3.1807e-05 loss=0.0549 total_loss=0.0482
2024-09-08 23:36:41,391 - train.py[line:525] - INFO: Epoch 253 validation result: mIoU 58.55, best mIoU 58.92
2024-09-08 23:36:41,392 - train.py[line:541] - INFO: Avg train time: 79.02s, avg eval time: 450.21s, left eval count: 247, ETA: 2024-09-10 11:55:19
2024-09-08 23:37:56,317 - train.py[line:384] - INFO: Epoch 254/500 Iter 100/100: lr=3.1691e-05 loss=0.0508 total_loss=0.0491
2024-09-08 23:45:25,969 - train.py[line:525] - INFO: Epoch 254 validation result: mIoU 58.2, best mIoU 58.92
2024-09-08 23:45:25,970 - train.py[line:541] - INFO: Avg train time: 76.89s, avg eval time: 449.99s, left eval count: 246, ETA: 2024-09-10 11:45:37
2024-09-08 23:46:44,973 - train.py[line:384] - INFO: Epoch 255/500 Iter 100/100: lr=3.1575e-05 loss=0.0476 total_loss=0.0493
2024-09-08 23:54:14,363 - train.py[line:525] - INFO: Epoch 255 validation result: mIoU 57.55, best mIoU 58.92
2024-09-08 23:54:14,363 - train.py[line:541] - INFO: Avg train time: 77.28s, avg eval time: 449.75s, left eval count: 245, ETA: 2024-09-10 11:46:16
2024-09-08 23:55:31,571 - train.py[line:384] - INFO: Epoch 256/500 Iter 100/100: lr=3.1459e-05 loss=0.0246 total_loss=0.0525
2024-09-09 00:03:01,348 - train.py[line:525] - INFO: Epoch 256 validation result: mIoU 58.62, best mIoU 58.92
2024-09-09 00:03:01,349 - train.py[line:541] - INFO: Avg train time: 76.72s, avg eval time: 449.76s, left eval count: 244, ETA: 2024-09-10 11:44:01
2024-09-09 00:04:17,741 - train.py[line:384] - INFO: Epoch 257/500 Iter 100/100: lr=3.1343e-05 loss=0.0576 total_loss=0.0493
2024-09-09 00:11:46,803 - train.py[line:525] - INFO: Epoch 257 validation result: mIoU 58.63, best mIoU 58.92
2024-09-09 00:11:46,804 - train.py[line:541] - INFO: Avg train time: 76.08s, avg eval time: 449.48s, left eval count: 243, ETA: 2024-09-10 11:40:18
2024-09-09 00:13:00,298 - train.py[line:384] - INFO: Epoch 258/500 Iter 100/100: lr=3.1227e-05 loss=0.0490 total_loss=0.0486
2024-09-09 00:20:30,078 - train.py[line:525] - INFO: Epoch 258 validation result: mIoU 58.73, best mIoU 58.92
2024-09-09 00:20:30,079 - train.py[line:541] - INFO: Avg train time: 74.50s, avg eval time: 449.60s, left eval count: 242, ETA: 2024-09-10 11:34:21
2024-09-09 00:21:51,291 - train.py[line:384] - INFO: Epoch 259/500 Iter 100/100: lr=3.1111e-05 loss=0.0525 total_loss=0.0466
2024-09-09 00:29:20,870 - train.py[line:525] - INFO: Epoch 259 validation result: mIoU 58.81, best mIoU 58.92
2024-09-09 00:29:20,870 - train.py[line:541] - INFO: Avg train time: 76.65s, avg eval time: 449.59s, left eval count: 241, ETA: 2024-09-10 11:43:05
2024-09-09 00:30:33,567 - train.py[line:384] - INFO: Epoch 260/500 Iter 100/100: lr=3.0995e-05 loss=0.0425 total_loss=0.0600
2024-09-09 00:38:04,307 - train.py[line:525] - INFO: Epoch 260 validation result: mIoU 57.78, best mIoU 58.92
2024-09-09 00:38:04,308 - train.py[line:541] - INFO: Avg train time: 74.65s, avg eval time: 450.05s, left eval count: 240, ETA: 2024-09-10 11:36:52
2024-09-09 00:39:15,524 - train.py[line:384] - INFO: Epoch 261/500 Iter 100/100: lr=3.0878e-05 loss=0.0571 total_loss=0.0515
2024-09-09 00:46:44,844 - train.py[line:525] - INFO: Epoch 261 validation result: mIoU 58.57, best mIoU 58.92
2024-09-09 00:46:44,845 - train.py[line:541] - INFO: Avg train time: 72.71s, avg eval time: 449.76s, left eval count: 239, ETA: 2024-09-10 11:27:55
2024-09-09 00:48:08,654 - train.py[line:384] - INFO: Epoch 262/500 Iter 100/100: lr=3.0762e-05 loss=0.0772 total_loss=0.0736
2024-09-09 00:55:38,938 - train.py[line:525] - INFO: Epoch 262 validation result: mIoU 57.52, best mIoU 58.92
2024-09-09 00:55:38,939 - train.py[line:541] - INFO: Avg train time: 76.56s, avg eval time: 449.97s, left eval count: 238, ETA: 2024-09-10 11:44:13
2024-09-09 00:56:53,622 - train.py[line:384] - INFO: Epoch 263/500 Iter 100/100: lr=3.0646e-05 loss=0.0310 total_loss=0.0593
2024-09-09 01:04:22,876 - train.py[line:525] - INFO: Epoch 263 validation result: mIoU 58.5, best mIoU 58.92
2024-09-09 01:04:22,877 - train.py[line:541] - INFO: Avg train time: 75.31s, avg eval time: 449.68s, left eval count: 237, ETA: 2024-09-10 11:38:05
2024-09-09 01:05:32,925 - train.py[line:384] - INFO: Epoch 264/500 Iter 100/100: lr=3.0529e-05 loss=0.0453 total_loss=0.0516
2024-09-09 01:13:03,132 - train.py[line:525] - INFO: Epoch 264 validation result: mIoU 58.21, best mIoU 58.92
2024-09-09 01:13:03,133 - train.py[line:541] - INFO: Avg train time: 72.83s, avg eval time: 449.89s, left eval count: 236, ETA: 2024-09-10 11:29:06
2024-09-09 01:14:24,820 - train.py[line:384] - INFO: Epoch 265/500 Iter 100/100: lr=3.0413e-05 loss=0.0436 total_loss=0.0475
2024-09-09 01:21:54,303 - train.py[line:525] - INFO: Epoch 265 validation result: mIoU 58.61, best mIoU 58.92
2024-09-09 01:21:54,303 - train.py[line:541] - INFO: Avg train time: 75.82s, avg eval time: 449.73s, left eval count: 235, ETA: 2024-09-10 11:40:18
2024-09-09 01:23:16,448 - train.py[line:384] - INFO: Epoch 266/500 Iter 100/100: lr=3.0296e-05 loss=0.0477 total_loss=0.0481
2024-09-09 01:30:46,411 - train.py[line:525] - INFO: Epoch 266 validation result: mIoU 58.35, best mIoU 58.92
2024-09-09 01:30:46,411 - train.py[line:541] - INFO: Avg train time: 77.87s, avg eval time: 449.82s, left eval count: 234, ETA: 2024-09-10 11:48:46
2024-09-09 01:32:00,084 - train.py[line:384] - INFO: Epoch 267/500 Iter 100/100: lr=3.0180e-05 loss=0.0636 total_loss=0.0470
2024-09-09 01:39:30,663 - train.py[line:525] - INFO: Epoch 267 validation result: mIoU 58.63, best mIoU 58.92
2024-09-09 01:39:30,664 - train.py[line:541] - INFO: Avg train time: 75.80s, avg eval time: 450.12s, left eval count: 233, ETA: 2024-09-10 11:41:51
2024-09-09 01:40:37,208 - train.py[line:384] - INFO: Epoch 268/500 Iter 100/100: lr=3.0063e-05 loss=0.0599 total_loss=0.0467
2024-09-09 01:48:07,630 - train.py[line:525] - INFO: Epoch 268 validation result: mIoU 58.82, best mIoU 58.92
2024-09-09 01:48:07,631 - train.py[line:541] - INFO: Avg train time: 71.49s, avg eval time: 450.24s, left eval count: 232, ETA: 2024-09-10 11:25:30
2024-09-09 01:49:17,943 - train.py[line:384] - INFO: Epoch 269/500 Iter 100/100: lr=2.9946e-05 loss=0.0604 total_loss=0.0459
2024-09-09 01:56:49,149 - engine.py[line:153] - INFO: remove inferior checkpoint: {'epoch': 90, 'metric': 57.48}
2024-09-09 01:56:49,149 - engine.py[line:101] - INFO: Saving checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-269_miou_59.0.pth
2024-09-09 01:56:53,449 - engine.py[line:124] - INFO: Save checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-269_miou_59.0.pth, Time usage:
	prepare checkpoint: 0.011002063751220703, IO: 4.2884840965271
2024-09-09 01:56:53,450 - train.py[line:525] - INFO: Epoch 269 validation result: mIoU 59.0, best mIoU 59.0
2024-09-09 01:56:53,451 - train.py[line:541] - INFO: Avg train time: 70.51s, avg eval time: 452.35s, left eval count: 231, ETA: 2024-09-10 11:29:54
2024-09-09 01:58:08,781 - train.py[line:384] - INFO: Epoch 270/500 Iter 100/100: lr=2.9830e-05 loss=0.0526 total_loss=0.0453
2024-09-09 02:05:39,036 - train.py[line:525] - INFO: Epoch 270 validation result: mIoU 58.97, best mIoU 59.0
2024-09-09 02:05:39,036 - train.py[line:541] - INFO: Avg train time: 72.10s, avg eval time: 451.51s, left eval count: 230, ETA: 2024-09-10 11:32:50
2024-09-09 02:07:01,359 - train.py[line:384] - INFO: Epoch 271/500 Iter 100/100: lr=2.9713e-05 loss=0.0545 total_loss=0.0460
2024-09-09 02:14:30,860 - train.py[line:525] - INFO: Epoch 271 validation result: mIoU 58.89, best mIoU 59.0
2024-09-09 02:14:30,861 - train.py[line:541] - INFO: Avg train time: 75.65s, avg eval time: 450.71s, left eval count: 229, ETA: 2024-09-10 11:43:27
2024-09-09 02:15:41,697 - train.py[line:384] - INFO: Epoch 272/500 Iter 100/100: lr=2.9596e-05 loss=0.0508 total_loss=0.0439
2024-09-09 02:23:11,604 - train.py[line:525] - INFO: Epoch 272 validation result: mIoU 58.7, best mIoU 59.0
2024-09-09 02:23:11,605 - train.py[line:541] - INFO: Avg train time: 73.22s, avg eval time: 450.39s, left eval count: 228, ETA: 2024-09-10 11:32:54
2024-09-09 02:24:22,908 - train.py[line:384] - INFO: Epoch 273/500 Iter 100/100: lr=2.9479e-05 loss=0.0406 total_loss=0.0445
2024-09-09 02:31:51,895 - train.py[line:525] - INFO: Epoch 273 validation result: mIoU 58.9, best mIoU 59.0
2024-09-09 02:31:51,896 - train.py[line:541] - INFO: Avg train time: 71.93s, avg eval time: 449.83s, left eval count: 227, ETA: 2024-09-10 11:25:50
2024-09-09 02:33:09,404 - train.py[line:384] - INFO: Epoch 274/500 Iter 100/100: lr=2.9363e-05 loss=0.0375 total_loss=0.0451
2024-09-09 02:40:40,198 - train.py[line:525] - INFO: Epoch 274 validation result: mIoU 58.8, best mIoU 59.0
2024-09-09 02:40:40,199 - train.py[line:541] - INFO: Avg train time: 73.55s, avg eval time: 450.21s, left eval count: 226, ETA: 2024-09-10 11:33:31
2024-09-09 02:41:54,822 - train.py[line:384] - INFO: Epoch 275/500 Iter 100/100: lr=2.9246e-05 loss=0.0364 total_loss=0.0444
2024-09-09 02:49:24,847 - engine.py[line:153] - INFO: remove inferior checkpoint: {'epoch': 100, 'metric': 58.04}
2024-09-09 02:49:24,847 - engine.py[line:101] - INFO: Saving checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-275_miou_59.02.pth
2024-09-09 02:49:28,217 - engine.py[line:124] - INFO: Save checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-275_miou_59.02.pth, Time usage:
	prepare checkpoint: 0.01024174690246582, IO: 3.359081506729126
2024-09-09 02:49:28,217 - train.py[line:525] - INFO: Epoch 275 validation result: mIoU 59.02, best mIoU 59.02
2024-09-09 02:49:28,218 - train.py[line:541] - INFO: Avg train time: 73.42s, avg eval time: 451.49s, left eval count: 225, ETA: 2024-09-10 11:37:52
2024-09-09 02:50:38,619 - train.py[line:384] - INFO: Epoch 276/500 Iter 100/100: lr=2.9129e-05 loss=0.0578 total_loss=0.0450
2024-09-09 02:58:08,683 - train.py[line:525] - INFO: Epoch 276 validation result: mIoU 58.93, best mIoU 59.02
2024-09-09 02:58:08,684 - train.py[line:541] - INFO: Avg train time: 71.92s, avg eval time: 450.92s, left eval count: 224, ETA: 2024-09-10 11:30:03
2024-09-09 02:59:30,577 - train.py[line:384] - INFO: Epoch 277/500 Iter 100/100: lr=2.9011e-05 loss=0.0443 total_loss=0.0448
2024-09-09 03:07:01,197 - train.py[line:525] - INFO: Epoch 277 validation result: mIoU 58.81, best mIoU 59.02
2024-09-09 03:07:01,198 - train.py[line:541] - INFO: Avg train time: 75.42s, avg eval time: 450.80s, left eval count: 223, ETA: 2024-09-10 11:42:47
2024-09-09 03:08:18,182 - train.py[line:384] - INFO: Epoch 278/500 Iter 100/100: lr=2.8894e-05 loss=0.0319 total_loss=0.0436
2024-09-09 03:15:47,508 - train.py[line:525] - INFO: Epoch 278 validation result: mIoU 58.51, best mIoU 59.02
2024-09-09 03:15:47,508 - train.py[line:541] - INFO: Avg train time: 75.68s, avg eval time: 450.21s, left eval count: 222, ETA: 2024-09-10 11:41:35
2024-09-09 03:17:03,744 - train.py[line:384] - INFO: Epoch 279/500 Iter 100/100: lr=2.8777e-05 loss=0.0387 total_loss=0.0449
2024-09-09 03:24:33,740 - train.py[line:525] - INFO: Epoch 279 validation result: mIoU 58.84, best mIoU 59.02
2024-09-09 03:24:33,741 - train.py[line:541] - INFO: Avg train time: 75.46s, avg eval time: 450.12s, left eval count: 221, ETA: 2024-09-10 11:40:27
2024-09-09 03:25:44,268 - train.py[line:384] - INFO: Epoch 280/500 Iter 100/100: lr=2.8660e-05 loss=0.0281 total_loss=0.0437
2024-09-09 03:33:14,059 - train.py[line:525] - INFO: Epoch 280 validation result: mIoU 58.85, best mIoU 59.02
2024-09-09 03:33:14,059 - train.py[line:541] - INFO: Avg train time: 72.98s, avg eval time: 449.99s, left eval count: 220, ETA: 2024-09-10 11:30:46
2024-09-09 03:34:26,565 - train.py[line:384] - INFO: Epoch 281/500 Iter 100/100: lr=2.8543e-05 loss=0.0298 total_loss=0.0437
2024-09-09 03:41:57,323 - train.py[line:525] - INFO: Epoch 281 validation result: mIoU 58.8, best mIoU 59.02
2024-09-09 03:41:57,324 - train.py[line:541] - INFO: Avg train time: 72.33s, avg eval time: 450.30s, left eval count: 219, ETA: 2024-09-10 11:29:32
2024-09-09 03:43:08,751 - train.py[line:384] - INFO: Epoch 282/500 Iter 100/100: lr=2.8425e-05 loss=0.0420 total_loss=0.0446
2024-09-09 03:50:38,344 - train.py[line:525] - INFO: Epoch 282 validation result: mIoU 58.63, best mIoU 59.02
2024-09-09 03:50:38,345 - train.py[line:541] - INFO: Avg train time: 71.49s, avg eval time: 450.02s, left eval count: 218, ETA: 2024-09-10 11:25:26
2024-09-09 03:52:02,113 - train.py[line:384] - INFO: Epoch 283/500 Iter 100/100: lr=2.8308e-05 loss=0.0575 total_loss=0.0475
2024-09-09 03:59:31,452 - train.py[line:525] - INFO: Epoch 283 validation result: mIoU 58.79, best mIoU 59.02
2024-09-09 03:59:31,453 - train.py[line:541] - INFO: Avg train time: 75.90s, avg eval time: 449.75s, left eval count: 217, ETA: 2024-09-10 11:40:36
2024-09-09 04:00:41,617 - train.py[line:384] - INFO: Epoch 284/500 Iter 100/100: lr=2.8191e-05 loss=0.0602 total_loss=0.0452
2024-09-09 04:08:10,401 - train.py[line:525] - INFO: Epoch 284 validation result: mIoU 58.75, best mIoU 59.02
2024-09-09 04:08:10,402 - train.py[line:541] - INFO: Avg train time: 73.24s, avg eval time: 449.36s, left eval count: 216, ETA: 2024-09-10 11:29:31
2024-09-09 04:09:34,031 - train.py[line:384] - INFO: Epoch 285/500 Iter 100/100: lr=2.8073e-05 loss=0.0367 total_loss=0.0456
2024-09-09 04:17:06,343 - train.py[line:525] - INFO: Epoch 285 validation result: mIoU 58.55, best mIoU 59.02
2024-09-09 04:17:06,344 - train.py[line:541] - INFO: Avg train time: 76.88s, avg eval time: 450.54s, left eval count: 215, ETA: 2024-09-10 11:47:02
2024-09-09 04:18:24,812 - train.py[line:384] - INFO: Epoch 286/500 Iter 100/100: lr=2.7956e-05 loss=0.0417 total_loss=0.0440
2024-09-09 04:25:55,045 - train.py[line:525] - INFO: Epoch 286 validation result: mIoU 58.21, best mIoU 59.02
2024-09-09 04:25:55,046 - train.py[line:541] - INFO: Avg train time: 77.14s, avg eval time: 450.42s, left eval count: 214, ETA: 2024-09-10 11:47:31
2024-09-09 04:27:09,920 - train.py[line:384] - INFO: Epoch 287/500 Iter 100/100: lr=2.7838e-05 loss=0.0432 total_loss=0.0428
2024-09-09 04:34:39,423 - train.py[line:525] - INFO: Epoch 287 validation result: mIoU 58.45, best mIoU 59.02
2024-09-09 04:34:39,423 - train.py[line:541] - INFO: Avg train time: 75.65s, avg eval time: 450.05s, left eval count: 213, ETA: 2024-09-10 11:40:54
2024-09-09 04:36:00,575 - train.py[line:384] - INFO: Epoch 288/500 Iter 100/100: lr=2.7720e-05 loss=0.0549 total_loss=0.0438
2024-09-09 04:43:30,571 - train.py[line:525] - INFO: Epoch 288 validation result: mIoU 58.76, best mIoU 59.02
2024-09-09 04:43:30,572 - train.py[line:541] - INFO: Avg train time: 77.31s, avg eval time: 450.03s, left eval count: 212, ETA: 2024-09-10 11:46:46
2024-09-09 04:44:49,549 - train.py[line:384] - INFO: Epoch 289/500 Iter 100/100: lr=2.7603e-05 loss=0.0403 total_loss=0.0441
2024-09-09 04:52:19,008 - train.py[line:525] - INFO: Epoch 289 validation result: mIoU 58.33, best mIoU 59.02
2024-09-09 04:52:19,009 - train.py[line:541] - INFO: Avg train time: 77.49s, avg eval time: 449.80s, left eval count: 211, ETA: 2024-09-10 11:46:38
2024-09-09 04:53:38,690 - train.py[line:384] - INFO: Epoch 290/500 Iter 100/100: lr=2.7485e-05 loss=0.0393 total_loss=0.0429
2024-09-09 05:01:09,111 - train.py[line:525] - INFO: Epoch 290 validation result: mIoU 58.72, best mIoU 59.02
2024-09-09 05:01:09,112 - train.py[line:541] - INFO: Avg train time: 77.85s, avg eval time: 450.05s, left eval count: 210, ETA: 2024-09-10 11:48:48
2024-09-09 05:02:29,996 - train.py[line:384] - INFO: Epoch 291/500 Iter 100/100: lr=2.7367e-05 loss=0.0378 total_loss=0.0420
2024-09-09 05:09:59,795 - train.py[line:525] - INFO: Epoch 291 validation result: mIoU 58.11, best mIoU 59.02
2024-09-09 05:09:59,796 - train.py[line:541] - INFO: Avg train time: 78.54s, avg eval time: 449.95s, left eval count: 209, ETA: 2024-09-10 11:50:55
2024-09-09 05:11:13,100 - train.py[line:384] - INFO: Epoch 292/500 Iter 100/100: lr=2.7249e-05 loss=0.0477 total_loss=0.0426
2024-09-09 05:18:42,472 - train.py[line:525] - INFO: Epoch 292 validation result: mIoU 58.63, best mIoU 59.02
2024-09-09 05:18:42,473 - train.py[line:541] - INFO: Avg train time: 76.01s, avg eval time: 449.72s, left eval count: 208, ETA: 2024-09-10 11:41:13
2024-09-09 05:20:00,294 - train.py[line:384] - INFO: Epoch 293/500 Iter 100/100: lr=2.7131e-05 loss=0.0519 total_loss=0.0430
2024-09-09 05:27:28,820 - train.py[line:525] - INFO: Epoch 293 validation result: mIoU 58.29, best mIoU 59.02
2024-09-09 05:27:28,821 - train.py[line:541] - INFO: Avg train time: 76.32s, avg eval time: 449.24s, left eval count: 207, ETA: 2024-09-10 11:40:40
2024-09-09 05:28:49,581 - train.py[line:384] - INFO: Epoch 294/500 Iter 100/100: lr=2.7013e-05 loss=0.0343 total_loss=0.0432
2024-09-09 05:36:18,993 - train.py[line:525] - INFO: Epoch 294 validation result: mIoU 58.27, best mIoU 59.02
2024-09-09 05:36:18,994 - train.py[line:541] - INFO: Avg train time: 77.67s, avg eval time: 449.31s, left eval count: 206, ETA: 2024-09-10 11:45:36
2024-09-09 05:37:34,047 - train.py[line:384] - INFO: Epoch 295/500 Iter 100/100: lr=2.6895e-05 loss=0.0528 total_loss=0.0426
2024-09-09 05:45:02,964 - train.py[line:525] - INFO: Epoch 295 validation result: mIoU 58.44, best mIoU 59.02
2024-09-09 05:45:02,965 - train.py[line:541] - INFO: Avg train time: 76.15s, avg eval time: 449.15s, left eval count: 205, ETA: 2024-09-10 11:39:49
2024-09-09 05:46:16,287 - train.py[line:384] - INFO: Epoch 296/500 Iter 100/100: lr=2.6777e-05 loss=0.0369 total_loss=0.0429
2024-09-09 05:53:45,419 - train.py[line:525] - INFO: Epoch 296 validation result: mIoU 58.36, best mIoU 59.02
2024-09-09 05:53:45,419 - train.py[line:541] - INFO: Avg train time: 74.56s, avg eval time: 449.14s, left eval count: 204, ETA: 2024-09-10 11:34:21
2024-09-09 05:54:50,845 - train.py[line:384] - INFO: Epoch 297/500 Iter 100/100: lr=2.6659e-05 loss=0.0300 total_loss=0.0422
2024-09-09 06:02:20,801 - train.py[line:525] - INFO: Epoch 297 validation result: mIoU 58.38, best mIoU 59.02
2024-09-09 06:02:20,801 - train.py[line:541] - INFO: Avg train time: 70.40s, avg eval time: 449.47s, left eval count: 203, ETA: 2024-09-10 11:21:14
2024-09-09 06:03:32,304 - train.py[line:384] - INFO: Epoch 298/500 Iter 100/100: lr=2.6541e-05 loss=0.0353 total_loss=0.0413
2024-09-09 06:11:01,678 - train.py[line:525] - INFO: Epoch 298 validation result: mIoU 58.13, best mIoU 59.02
2024-09-09 06:11:01,678 - train.py[line:541] - INFO: Avg train time: 70.36s, avg eval time: 449.43s, left eval count: 202, ETA: 2024-09-10 11:21:00
2024-09-09 06:12:18,671 - train.py[line:384] - INFO: Epoch 299/500 Iter 100/100: lr=2.6423e-05 loss=0.0412 total_loss=0.0411
2024-09-09 06:19:48,127 - train.py[line:525] - INFO: Epoch 299 validation result: mIoU 58.51, best mIoU 59.02
2024-09-09 06:19:48,127 - train.py[line:541] - INFO: Avg train time: 72.60s, avg eval time: 449.44s, left eval count: 201, ETA: 2024-09-10 11:28:38
2024-09-09 06:21:11,115 - train.py[line:384] - INFO: Epoch 300/500 Iter 100/100: lr=2.6304e-05 loss=0.0314 total_loss=0.0417
2024-09-09 06:28:39,870 - train.py[line:525] - INFO: Epoch 300 validation result: mIoU 58.56, best mIoU 59.02
2024-09-09 06:28:39,871 - train.py[line:541] - INFO: Avg train time: 76.33s, avg eval time: 449.17s, left eval count: 200, ETA: 2024-09-10 11:40:18
2024-09-09 06:30:03,152 - train.py[line:384] - INFO: Epoch 301/500 Iter 100/100: lr=2.6186e-05 loss=0.0262 total_loss=0.0408
2024-09-09 06:37:32,441 - train.py[line:525] - INFO: Epoch 301 validation result: mIoU 58.53, best mIoU 59.02
2024-09-09 06:37:32,442 - train.py[line:541] - INFO: Avg train time: 78.78s, avg eval time: 449.22s, left eval count: 199, ETA: 2024-09-10 11:48:43
2024-09-09 06:38:38,835 - train.py[line:384] - INFO: Epoch 302/500 Iter 100/100: lr=2.6067e-05 loss=0.0513 total_loss=0.0417
2024-09-09 06:46:08,012 - train.py[line:525] - INFO: Epoch 302 validation result: mIoU 58.81, best mIoU 59.02
2024-09-09 06:46:08,013 - train.py[line:541] - INFO: Avg train time: 73.44s, avg eval time: 449.20s, left eval count: 198, ETA: 2024-09-10 11:30:50
2024-09-09 06:47:23,445 - train.py[line:384] - INFO: Epoch 303/500 Iter 100/100: lr=2.5949e-05 loss=0.0449 total_loss=0.0416
2024-09-09 06:54:52,306 - train.py[line:525] - INFO: Epoch 303 validation result: mIoU 58.83, best mIoU 59.02
2024-09-09 06:54:52,306 - train.py[line:541] - INFO: Avg train time: 73.77s, avg eval time: 449.06s, left eval count: 197, ETA: 2024-09-10 11:31:29
2024-09-09 06:56:02,503 - train.py[line:384] - INFO: Epoch 304/500 Iter 100/100: lr=2.5830e-05 loss=0.0702 total_loss=0.0422
2024-09-09 07:03:31,888 - train.py[line:525] - INFO: Epoch 304 validation result: mIoU 58.5, best mIoU 59.02
2024-09-09 07:03:31,889 - train.py[line:541] - INFO: Avg train time: 71.94s, avg eval time: 449.19s, left eval count: 196, ETA: 2024-09-10 11:25:54
2024-09-09 07:04:51,727 - train.py[line:384] - INFO: Epoch 305/500 Iter 100/100: lr=2.5712e-05 loss=0.0228 total_loss=0.0424
2024-09-09 07:12:20,038 - train.py[line:525] - INFO: Epoch 305 validation result: mIoU 58.3, best mIoU 59.02
2024-09-09 07:12:20,039 - train.py[line:541] - INFO: Avg train time: 74.62s, avg eval time: 448.84s, left eval count: 195, ETA: 2024-09-10 11:33:35
2024-09-09 07:13:34,319 - train.py[line:384] - INFO: Epoch 306/500 Iter 100/100: lr=2.5593e-05 loss=0.0525 total_loss=0.0408
2024-09-09 07:21:03,246 - train.py[line:525] - INFO: Epoch 306 validation result: mIoU 58.56, best mIoU 59.02
2024-09-09 07:21:03,246 - train.py[line:541] - INFO: Avg train time: 74.10s, avg eval time: 448.87s, left eval count: 194, ETA: 2024-09-10 11:31:59
2024-09-09 07:22:16,057 - train.py[line:384] - INFO: Epoch 307/500 Iter 100/100: lr=2.5474e-05 loss=0.0460 total_loss=0.0408
2024-09-09 07:29:45,240 - train.py[line:525] - INFO: Epoch 307 validation result: mIoU 58.6, best mIoU 59.02
2024-09-09 07:29:45,241 - train.py[line:541] - INFO: Avg train time: 73.16s, avg eval time: 449.00s, left eval count: 193, ETA: 2024-09-10 11:29:21
2024-09-09 07:31:01,834 - train.py[line:384] - INFO: Epoch 308/500 Iter 100/100: lr=2.5355e-05 loss=0.0397 total_loss=0.0404
2024-09-09 07:38:31,308 - train.py[line:525] - INFO: Epoch 308 validation result: mIoU 58.8, best mIoU 59.02
2024-09-09 07:38:31,309 - train.py[line:541] - INFO: Avg train time: 74.15s, avg eval time: 449.19s, left eval count: 192, ETA: 2024-09-10 11:33:11
2024-09-09 07:39:36,942 - train.py[line:384] - INFO: Epoch 309/500 Iter 100/100: lr=2.5236e-05 loss=0.0368 total_loss=0.0402
2024-09-09 07:47:05,981 - train.py[line:525] - INFO: Epoch 309 validation result: mIoU 58.84, best mIoU 59.02
2024-09-09 07:47:05,982 - train.py[line:541] - INFO: Avg train time: 70.25s, avg eval time: 449.13s, left eval count: 191, ETA: 2024-09-10 11:20:27
2024-09-09 07:48:25,291 - train.py[line:384] - INFO: Epoch 310/500 Iter 100/100: lr=2.5118e-05 loss=0.0460 total_loss=0.0422
2024-09-09 07:55:54,273 - train.py[line:525] - INFO: Epoch 310 validation result: mIoU 58.67, best mIoU 59.02
2024-09-09 07:55:54,273 - train.py[line:541] - INFO: Avg train time: 73.42s, avg eval time: 449.07s, left eval count: 190, ETA: 2024-09-10 11:30:28
2024-09-09 07:57:19,681 - train.py[line:384] - INFO: Epoch 311/500 Iter 100/100: lr=2.4999e-05 loss=0.0324 total_loss=0.0399
2024-09-09 08:04:48,604 - train.py[line:525] - INFO: Epoch 311 validation result: mIoU 58.93, best mIoU 59.02
2024-09-09 08:04:48,605 - train.py[line:541] - INFO: Avg train time: 77.61s, avg eval time: 449.01s, left eval count: 189, ETA: 2024-09-10 11:43:40
2024-09-09 08:06:08,359 - train.py[line:384] - INFO: Epoch 312/500 Iter 100/100: lr=2.4879e-05 loss=0.0520 total_loss=0.0410
2024-09-09 08:13:38,679 - train.py[line:525] - INFO: Epoch 312 validation result: mIoU 58.98, best mIoU 59.02
2024-09-09 08:13:38,680 - train.py[line:541] - INFO: Avg train time: 77.95s, avg eval time: 449.53s, left eval count: 188, ETA: 2024-09-10 11:46:26
2024-09-09 08:14:54,982 - train.py[line:384] - INFO: Epoch 313/500 Iter 100/100: lr=2.4760e-05 loss=0.0456 total_loss=0.0396
2024-09-09 08:22:24,767 - train.py[line:525] - INFO: Epoch 313 validation result: mIoU 58.9, best mIoU 59.02
2024-09-09 08:22:24,768 - train.py[line:541] - INFO: Avg train time: 76.81s, avg eval time: 449.64s, left eval count: 187, ETA: 2024-09-10 11:43:09
2024-09-09 08:23:33,314 - train.py[line:384] - INFO: Epoch 314/500 Iter 100/100: lr=2.4641e-05 loss=0.0306 total_loss=0.0387
2024-09-09 08:31:02,785 - engine.py[line:153] - INFO: remove inferior checkpoint: {'epoch': 120, 'metric': 58.62}
2024-09-09 08:31:02,786 - engine.py[line:101] - INFO: Saving checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-314_miou_59.07.pth
2024-09-09 08:31:07,460 - engine.py[line:124] - INFO: Save checkpoint to file /defaultShare/archive/yinbowen/VIT/Finetuning/checkpoints/NYUDepthv2_MLP_v4_base_20240908-142545/epoch-314_miou_59.07.pth, Time usage:
	prepare checkpoint: 0.014937400817871094, IO: 4.6598005294799805
2024-09-09 08:31:07,462 - train.py[line:525] - INFO: Epoch 314 validation result: mIoU 59.07, best mIoU 59.07
2024-09-09 08:31:07,463 - train.py[line:541] - INFO: Avg train time: 73.04s, avg eval time: 451.44s, left eval count: 186, ETA: 2024-09-10 11:37:01
2024-09-09 08:32:15,891 - train.py[line:384] - INFO: Epoch 315/500 Iter 100/100: lr=2.4522e-05 loss=0.0363 total_loss=0.0417
2024-09-09 08:39:44,275 - train.py[line:525] - INFO: Epoch 315 validation result: mIoU 58.64, best mIoU 59.07
2024-09-09 08:39:44,276 - train.py[line:541] - INFO: Avg train time: 70.91s, avg eval time: 450.22s, left eval count: 185, ETA: 2024-09-10 11:26:32
2024-09-09 08:41:03,933 - train.py[line:384] - INFO: Epoch 316/500 Iter 100/100: lr=2.4403e-05 loss=0.0357 total_loss=0.0425
2024-09-09 08:48:33,000 - train.py[line:525] - INFO: Epoch 316 validation result: mIoU 58.46, best mIoU 59.07
2024-09-09 08:48:33,001 - train.py[line:541] - INFO: Avg train time: 74.05s, avg eval time: 449.76s, left eval count: 184, ETA: 2024-09-10 11:34:52
2024-09-09 08:49:45,855 - train.py[line:384] - INFO: Epoch 317/500 Iter 100/100: lr=2.4283e-05 loss=0.0277 total_loss=0.0414
2024-09-09 08:57:15,274 - train.py[line:525] - INFO: Epoch 317 validation result: mIoU 57.36, best mIoU 59.07
2024-09-09 08:57:15,274 - train.py[line:541] - INFO: Avg train time: 73.13s, avg eval time: 449.62s, left eval count: 183, ETA: 2024-09-10 11:31:39
2024-09-09 08:58:23,274 - train.py[line:384] - INFO: Epoch 318/500 Iter 100/100: lr=2.4164e-05 loss=0.0343 total_loss=0.0485
2024-09-09 09:05:52,843 - train.py[line:525] - INFO: Epoch 318 validation result: mIoU 58.54, best mIoU 59.07
2024-09-09 09:05:52,844 - train.py[line:541] - INFO: Avg train time: 70.63s, avg eval time: 449.60s, left eval count: 182, ETA: 2024-09-10 11:23:55
2024-09-09 09:07:13,846 - train.py[line:384] - INFO: Epoch 319/500 Iter 100/100: lr=2.4044e-05 loss=0.0522 total_loss=0.0422
2024-09-09 09:14:42,890 - train.py[line:525] - INFO: Epoch 319 validation result: mIoU 58.25, best mIoU 59.07
2024-09-09 09:14:42,891 - train.py[line:541] - INFO: Avg train time: 74.36s, avg eval time: 449.38s, left eval count: 181, ETA: 2024-09-10 11:34:39
2024-09-09 09:16:07,872 - train.py[line:384] - INFO: Epoch 320/500 Iter 100/100: lr=2.3925e-05 loss=0.0596 total_loss=0.0406
2024-09-09 09:23:37,260 - train.py[line:525] - INFO: Epoch 320 validation result: mIoU 58.68, best mIoU 59.07
2024-09-09 09:23:37,260 - train.py[line:541] - INFO: Avg train time: 78.13s, avg eval time: 449.38s, left eval count: 180, ETA: 2024-09-10 11:46:09
2024-09-09 09:24:47,410 - train.py[line:384] - INFO: Epoch 321/500 Iter 100/100: lr=2.3805e-05 loss=0.0439 total_loss=0.0395
2024-09-09 09:32:15,709 - train.py[line:525] - INFO: Epoch 321 validation result: mIoU 59.07, best mIoU 59.07
2024-09-09 09:32:15,710 - train.py[line:541] - INFO: Avg train time: 74.55s, avg eval time: 448.95s, left eval count: 179, ETA: 2024-09-10 11:34:02
2024-09-09 09:33:25,923 - train.py[line:384] - INFO: Epoch 322/500 Iter 100/100: lr=2.3685e-05 loss=0.0441 total_loss=0.0385
2024-09-09 09:40:55,080 - train.py[line:525] - INFO: Epoch 322 validation result: mIoU 58.77, best mIoU 59.07
2024-09-09 09:40:55,081 - train.py[line:541] - INFO: Avg train time: 72.38s, avg eval time: 449.03s, left eval count: 178, ETA: 2024-09-10 11:27:47
2024-09-09 09:42:14,159 - train.py[line:384] - INFO: Epoch 323/500 Iter 100/100: lr=2.3565e-05 loss=0.0330 total_loss=0.0388
2024-09-09 09:49:42,165 - train.py[line:525] - INFO: Epoch 323 validation result: mIoU 58.65, best mIoU 59.07
2024-09-09 09:49:42,165 - train.py[line:541] - INFO: Avg train time: 74.57s, avg eval time: 448.62s, left eval count: 177, ETA: 2024-09-10 11:33:07
2024-09-09 09:50:52,254 - train.py[line:384] - INFO: Epoch 324/500 Iter 100/100: lr=2.3446e-05 loss=0.0425 total_loss=0.0390
2024-09-09 09:58:22,451 - train.py[line:525] - INFO: Epoch 324 validation result: mIoU 58.69, best mIoU 59.07
2024-09-09 09:58:22,451 - train.py[line:541] - INFO: Avg train time: 72.32s, avg eval time: 449.25s, left eval count: 176, ETA: 2024-09-10 11:28:19
2024-09-09 09:59:43,286 - train.py[line:384] - INFO: Epoch 325/500 Iter 100/100: lr=2.3326e-05 loss=0.0453 total_loss=0.0384
2024-09-09 10:07:11,883 - train.py[line:525] - INFO: Epoch 325 validation result: mIoU 58.59, best mIoU 59.07
2024-09-09 10:07:11,883 - train.py[line:541] - INFO: Avg train time: 75.22s, avg eval time: 448.99s, left eval count: 175, ETA: 2024-09-10 11:36:09
2024-09-09 10:08:27,627 - train.py[line:384] - INFO: Epoch 326/500 Iter 100/100: lr=2.3206e-05 loss=0.0574 total_loss=0.0393
2024-09-09 10:15:56,494 - train.py[line:525] - INFO: Epoch 326 validation result: mIoU 58.8, best mIoU 59.07
2024-09-09 10:15:56,495 - train.py[line:541] - INFO: Avg train time: 74.99s, avg eval time: 448.94s, left eval count: 174, ETA: 2024-09-10 11:35:19
2024-09-09 10:17:16,138 - train.py[line:384] - INFO: Epoch 327/500 Iter 100/100: lr=2.3086e-05 loss=0.0385 total_loss=0.0382
2024-09-09 10:24:45,625 - train.py[line:525] - INFO: Epoch 327 validation result: mIoU 58.85, best mIoU 59.07
2024-09-09 10:24:45,626 - train.py[line:541] - INFO: Avg train time: 76.40s, avg eval time: 449.16s, left eval count: 173, ETA: 2024-09-10 11:40:08
2024-09-09 10:25:59,272 - train.py[line:384] - INFO: Epoch 328/500 Iter 100/100: lr=2.2966e-05 loss=0.0420 total_loss=0.0389
2024-09-09 10:33:27,613 - train.py[line:525] - INFO: Epoch 328 validation result: mIoU 58.54, best mIoU 59.07
2024-09-09 10:33:27,614 - train.py[line:541] - INFO: Avg train time: 74.80s, avg eval time: 448.83s, left eval count: 172, ETA: 2024-09-10 11:34:32
2024-09-09 10:34:48,970 - train.py[line:384] - INFO: Epoch 329/500 Iter 100/100: lr=2.2845e-05 loss=0.0442 total_loss=0.0386
2024-09-09 10:42:18,430 - train.py[line:525] - INFO: Epoch 329 validation result: mIoU 58.44, best mIoU 59.07
2024-09-09 10:42:18,431 - train.py[line:541] - INFO: Avg train time: 76.94s, avg eval time: 449.08s, left eval count: 171, ETA: 2024-09-10 11:41:28
2024-09-09 10:43:36,338 - train.py[line:384] - INFO: Epoch 330/500 Iter 100/100: lr=2.2725e-05 loss=0.0375 total_loss=0.0413
2024-09-09 10:51:05,614 - train.py[line:525] - INFO: Epoch 330 validation result: mIoU 58.25, best mIoU 59.07
2024-09-09 10:51:05,615 - train.py[line:541] - INFO: Avg train time: 76.91s, avg eval time: 449.16s, left eval count: 170, ETA: 2024-09-10 11:41:37
2024-09-09 10:52:22,331 - train.py[line:384] - INFO: Epoch 331/500 Iter 100/100: lr=2.2605e-05 loss=0.0493 total_loss=0.0395
2024-09-09 10:59:51,478 - train.py[line:525] - INFO: Epoch 331 validation result: mIoU 58.54, best mIoU 59.07
2024-09-09 10:59:51,479 - train.py[line:541] - INFO: Avg train time: 76.44s, avg eval time: 449.16s, left eval count: 169, ETA: 2024-09-10 11:40:16
2024-09-09 11:01:05,316 - train.py[line:384] - INFO: Epoch 332/500 Iter 100/100: lr=2.2484e-05 loss=0.0309 total_loss=0.0391
2024-09-09 11:08:33,917 - train.py[line:525] - INFO: Epoch 332 validation result: mIoU 58.39, best mIoU 59.07
2024-09-09 11:08:33,918 - train.py[line:541] - INFO: Avg train time: 74.90s, avg eval time: 448.93s, left eval count: 168, ETA: 2024-09-10 11:35:18
2024-09-09 11:09:43,423 - train.py[line:384] - INFO: Epoch 333/500 Iter 100/100: lr=2.2364e-05 loss=0.0274 total_loss=0.0386
2024-09-09 11:17:12,226 - train.py[line:525] - INFO: Epoch 333 validation result: mIoU 58.51, best mIoU 59.07
2024-09-09 11:17:12,227 - train.py[line:541] - INFO: Avg train time: 72.34s, avg eval time: 448.88s, left eval count: 167, ETA: 2024-09-10 11:27:56
2024-09-09 11:18:25,882 - train.py[line:384] - INFO: Epoch 334/500 Iter 100/100: lr=2.2243e-05 loss=0.0404 total_loss=0.0375
2024-09-09 11:25:56,208 - train.py[line:525] - INFO: Epoch 334 validation result: mIoU 58.4, best mIoU 59.07
2024-09-09 11:25:56,209 - train.py[line:541] - INFO: Avg train time: 72.36s, avg eval time: 449.46s, left eval count: 166, ETA: 2024-09-10 11:29:38
2024-09-09 11:27:09,858 - train.py[line:384] - INFO: Epoch 335/500 Iter 100/100: lr=2.2123e-05 loss=0.0574 total_loss=0.0390
2024-09-09 11:34:39,545 - train.py[line:525] - INFO: Epoch 335 validation result: mIoU 58.42, best mIoU 59.07
2024-09-09 11:34:39,546 - train.py[line:541] - INFO: Avg train time: 72.31s, avg eval time: 449.55s, left eval count: 165, ETA: 2024-09-10 11:29:47
2024-09-09 11:35:58,130 - train.py[line:384] - INFO: Epoch 336/500 Iter 100/100: lr=2.2002e-05 loss=0.0201 total_loss=0.0382
2024-09-09 11:43:27,902 - train.py[line:525] - INFO: Epoch 336 validation result: mIoU 58.62, best mIoU 59.07
2024-09-09 11:43:27,903 - train.py[line:541] - INFO: Avg train time: 74.32s, avg eval time: 449.64s, left eval count: 164, ETA: 2024-09-10 11:35:36
2024-09-09 11:44:51,293 - train.py[line:384] - INFO: Epoch 337/500 Iter 100/100: lr=2.1881e-05 loss=0.0271 total_loss=0.0370
2024-09-09 11:52:20,765 - train.py[line:525] - INFO: Epoch 337 validation result: mIoU 58.3, best mIoU 59.07
2024-09-09 11:52:20,766 - train.py[line:541] - INFO: Avg train time: 77.55s, avg eval time: 449.57s, left eval count: 163, ETA: 2024-09-10 11:44:22
2024-09-09 11:53:38,792 - train.py[line:384] - INFO: Epoch 338/500 Iter 100/100: lr=2.1760e-05 loss=0.0444 total_loss=0.0388
2024-09-09 12:01:07,982 - train.py[line:525] - INFO: Epoch 338 validation result: mIoU 58.47, best mIoU 59.07
2024-09-09 12:01:07,982 - train.py[line:541] - INFO: Avg train time: 77.24s, avg eval time: 449.42s, left eval count: 162, ETA: 2024-09-10 11:43:06
2024-09-09 12:02:19,729 - train.py[line:384] - INFO: Epoch 339/500 Iter 100/100: lr=2.1639e-05 loss=0.0395 total_loss=0.0393
2024-09-09 12:09:48,619 - train.py[line:525] - INFO: Epoch 339 validation result: mIoU 58.63, best mIoU 59.07
2024-09-09 12:09:48,620 - train.py[line:541] - INFO: Avg train time: 74.64s, avg eval time: 449.21s, left eval count: 161, ETA: 2024-09-10 11:35:28
2024-09-09 12:11:12,688 - train.py[line:384] - INFO: Epoch 340/500 Iter 100/100: lr=2.1518e-05 loss=0.0557 total_loss=0.0384
2024-09-09 12:18:42,138 - train.py[line:525] - INFO: Epoch 340 validation result: mIoU 58.69, best mIoU 59.07
2024-09-09 12:18:42,139 - train.py[line:541] - INFO: Avg train time: 78.09s, avg eval time: 449.30s, left eval count: 160, ETA: 2024-09-10 11:45:05
2024-09-09 12:19:58,668 - train.py[line:384] - INFO: Epoch 341/500 Iter 100/100: lr=2.1397e-05 loss=0.0319 total_loss=0.0368
2024-09-09 12:27:27,109 - train.py[line:525] - INFO: Epoch 341 validation result: mIoU 58.46, best mIoU 59.07
2024-09-09 12:27:27,110 - train.py[line:541] - INFO: Avg train time: 77.08s, avg eval time: 448.96s, left eval count: 159, ETA: 2024-09-10 11:41:27
2024-09-09 12:28:44,091 - train.py[line:384] - INFO: Epoch 342/500 Iter 100/100: lr=2.1276e-05 loss=0.0404 total_loss=0.0371
2024-09-09 12:36:12,725 - train.py[line:525] - INFO: Epoch 342 validation result: mIoU 58.84, best mIoU 59.07
2024-09-09 12:36:12,726 - train.py[line:541] - INFO: Avg train time: 76.56s, avg eval time: 448.83s, left eval count: 158, ETA: 2024-09-10 11:39:44
2024-09-09 12:37:28,441 - train.py[line:384] - INFO: Epoch 343/500 Iter 100/100: lr=2.1155e-05 loss=0.0277 total_loss=0.0379
2024-09-09 12:44:56,404 - train.py[line:525] - INFO: Epoch 343 validation result: mIoU 58.9, best mIoU 59.07
2024-09-09 12:44:56,405 - train.py[line:541] - INFO: Avg train time: 75.79s, avg eval time: 448.48s, left eval count: 157, ETA: 2024-09-10 11:36:46
2024-09-09 12:46:14,268 - train.py[line:384] - INFO: Epoch 344/500 Iter 100/100: lr=2.1034e-05 loss=0.0329 total_loss=0.0379
2024-09-09 12:53:43,118 - train.py[line:525] - INFO: Epoch 344 validation result: mIoU 58.72, best mIoU 59.07
2024-09-09 12:53:43,119 - train.py[line:541] - INFO: Avg train time: 76.18s, avg eval time: 448.63s, left eval count: 156, ETA: 2024-09-10 11:38:13
2024-09-09 12:55:05,930 - train.py[line:384] - INFO: Epoch 345/500 Iter 100/100: lr=2.0912e-05 loss=0.0479 total_loss=0.0616
2024-09-09 13:02:34,082 - train.py[line:525] - INFO: Epoch 345 validation result: mIoU 58.32, best mIoU 59.07
2024-09-09 13:02:34,083 - train.py[line:541] - INFO: Avg train time: 78.37s, avg eval time: 448.44s, left eval count: 155, ETA: 2024-09-10 11:43:28
2024-09-09 13:03:48,789 - train.py[line:384] - INFO: Epoch 346/500 Iter 100/100: lr=2.0791e-05 loss=0.0677 total_loss=0.0402
2024-09-09 13:11:16,999 - train.py[line:525] - INFO: Epoch 346 validation result: mIoU 58.14, best mIoU 59.07
2024-09-09 13:11:17,000 - train.py[line:541] - INFO: Avg train time: 76.53s, avg eval time: 448.35s, left eval count: 154, ETA: 2024-09-10 11:38:28
2024-09-09 13:12:31,173 - train.py[line:384] - INFO: Epoch 347/500 Iter 100/100: lr=2.0669e-05 loss=0.0421 total_loss=0.0393
2024-09-09 13:19:59,374 - train.py[line:525] - INFO: Epoch 347 validation result: mIoU 58.47, best mIoU 59.07
2024-09-09 13:19:59,375 - train.py[line:541] - INFO: Avg train time: 75.07s, avg eval time: 448.29s, left eval count: 153, ETA: 2024-09-10 11:34:33
2024-09-09 13:21:13,028 - train.py[line:384] - INFO: Epoch 348/500 Iter 100/100: lr=2.0548e-05 loss=0.0455 total_loss=0.0384
2024-09-09 13:28:43,170 - train.py[line:525] - INFO: Epoch 348 validation result: mIoU 58.53, best mIoU 59.07
2024-09-09 13:28:43,171 - train.py[line:541] - INFO: Avg train time: 74.12s, avg eval time: 449.03s, left eval count: 152, ETA: 2024-09-10 11:34:02
2024-09-09 13:29:58,207 - train.py[line:384] - INFO: Epoch 349/500 Iter 100/100: lr=2.0426e-05 loss=0.0322 total_loss=0.0374
2024-09-09 13:37:27,131 - train.py[line:525] - INFO: Epoch 349 validation result: mIoU 58.64, best mIoU 59.07
2024-09-09 13:37:27,132 - train.py[line:541] - INFO: Avg train time: 74.00s, avg eval time: 448.99s, left eval count: 151, ETA: 2024-09-10 11:33:38
2024-09-09 13:38:48,484 - train.py[line:384] - INFO: Epoch 350/500 Iter 100/100: lr=2.0304e-05 loss=0.0258 total_loss=0.0373
2024-09-09 13:46:16,907 - train.py[line:525] - INFO: Epoch 350 validation result: mIoU 58.71, best mIoU 59.07
2024-09-09 13:46:16,908 - train.py[line:541] - INFO: Avg train time: 76.47s, avg eval time: 448.76s, left eval count: 150, ETA: 2024-09-10 11:39:21
2024-09-09 13:47:43,352 - train.py[line:384] - INFO: Epoch 351/500 Iter 100/100: lr=2.0182e-05 loss=0.0373 total_loss=0.0382
2024-09-09 13:55:11,782 - train.py[line:525] - INFO: Epoch 351 validation result: mIoU 58.31, best mIoU 59.07
2024-09-09 13:55:11,782 - train.py[line:541] - INFO: Avg train time: 79.91s, avg eval time: 448.63s, left eval count: 149, ETA: 2024-09-10 11:47:44
2024-09-09 13:56:22,811 - train.py[line:384] - INFO: Epoch 352/500 Iter 100/100: lr=2.0060e-05 loss=0.0362 total_loss=0.0357
2024-09-09 14:03:51,616 - train.py[line:525] - INFO: Epoch 352 validation result: mIoU 58.44, best mIoU 59.07
2024-09-09 14:03:51,617 - train.py[line:541] - INFO: Avg train time: 75.90s, avg eval time: 448.70s, left eval count: 148, ETA: 2024-09-10 11:37:52
2024-09-09 14:05:10,716 - train.py[line:384] - INFO: Epoch 353/500 Iter 100/100: lr=1.9938e-05 loss=0.0303 total_loss=0.0362
2024-09-09 14:12:39,445 - train.py[line:525] - INFO: Epoch 353 validation result: mIoU 58.43, best mIoU 59.07
2024-09-09 14:12:39,446 - train.py[line:541] - INFO: Avg train time: 76.78s, avg eval time: 448.71s, left eval count: 147, ETA: 2024-09-10 11:40:07
2024-09-09 14:14:03,689 - train.py[line:384] - INFO: Epoch 354/500 Iter 100/100: lr=1.9816e-05 loss=0.0313 total_loss=0.0363
2024-09-09 14:21:32,582 - train.py[line:525] - INFO: Epoch 354 validation result: mIoU 58.0, best mIoU 59.07
2024-09-09 14:21:32,583 - train.py[line:541] - INFO: Avg train time: 79.31s, avg eval time: 448.78s, left eval count: 146, ETA: 2024-09-10 11:46:34
2024-09-09 14:22:47,598 - train.py[line:384] - INFO: Epoch 355/500 Iter 100/100: lr=1.9694e-05 loss=0.0327 total_loss=0.0364
2024-09-09 14:30:17,278 - train.py[line:525] - INFO: Epoch 355 validation result: mIoU 58.52, best mIoU 59.07
2024-09-09 14:30:17,278 - train.py[line:541] - INFO: Avg train time: 77.14s, avg eval time: 449.14s, left eval count: 145, ETA: 2024-09-10 11:42:08
2024-09-09 14:31:38,411 - train.py[line:384] - INFO: Epoch 356/500 Iter 100/100: lr=1.9572e-05 loss=0.0465 total_loss=0.0360
2024-09-09 14:39:07,879 - train.py[line:525] - INFO: Epoch 356 validation result: mIoU 58.33, best mIoU 59.07
2024-09-09 14:39:07,880 - train.py[line:541] - INFO: Avg train time: 78.29s, avg eval time: 449.27s, left eval count: 144, ETA: 2024-09-10 11:45:17
2024-09-09 14:40:24,456 - train.py[line:384] - INFO: Epoch 357/500 Iter 100/100: lr=1.9449e-05 loss=0.0315 total_loss=0.0382
2024-09-09 14:47:53,681 - train.py[line:525] - INFO: Epoch 357 validation result: mIoU 58.49, best mIoU 59.07
2024-09-09 14:47:53,681 - train.py[line:541] - INFO: Avg train time: 77.12s, avg eval time: 449.25s, left eval count: 143, ETA: 2024-09-10 11:42:25
2024-09-09 14:49:12,367 - train.py[line:384] - INFO: Epoch 358/500 Iter 100/100: lr=1.9327e-05 loss=0.0304 total_loss=0.0370
2024-09-09 14:56:41,329 - train.py[line:525] - INFO: Epoch 358 validation result: mIoU 58.47, best mIoU 59.07
2024-09-09 14:56:41,330 - train.py[line:541] - INFO: Avg train time: 77.29s, avg eval time: 449.14s, left eval count: 142, ETA: 2024-09-10 11:42:34
2024-09-09 14:57:53,942 - train.py[line:384] - INFO: Epoch 359/500 Iter 100/100: lr=1.9205e-05 loss=0.0292 total_loss=0.0359
2024-09-09 15:05:23,160 - train.py[line:525] - INFO: Epoch 359 validation result: mIoU 58.29, best mIoU 59.07
2024-09-09 15:05:23,160 - train.py[line:541] - INFO: Avg train time: 75.00s, avg eval time: 449.17s, left eval count: 141, ETA: 2024-09-10 11:37:10
2024-09-09 15:06:44,991 - train.py[line:384] - INFO: Epoch 360/500 Iter 100/100: lr=1.9082e-05 loss=0.0364 total_loss=0.0352
2024-09-09 15:14:13,811 - train.py[line:525] - INFO: Epoch 360 validation result: mIoU 58.4, best mIoU 59.07
2024-09-09 15:14:13,811 - train.py[line:541] - INFO: Avg train time: 77.22s, avg eval time: 449.03s, left eval count: 140, ETA: 2024-09-10 11:42:09
2024-09-09 15:15:19,657 - train.py[line:384] - INFO: Epoch 361/500 Iter 100/100: lr=1.8959e-05 loss=0.0410 total_loss=0.0365
2024-09-09 15:22:47,992 - train.py[line:525] - INFO: Epoch 361 validation result: mIoU 57.99, best mIoU 59.07
2024-09-09 15:22:47,993 - train.py[line:541] - INFO: Avg train time: 72.32s, avg eval time: 448.75s, left eval count: 139, ETA: 2024-09-10 11:29:56
2024-09-09 15:24:07,054 - train.py[line:384] - INFO: Epoch 362/500 Iter 100/100: lr=1.8836e-05 loss=0.0391 total_loss=0.0378
2024-09-09 15:31:35,744 - train.py[line:525] - INFO: Epoch 362 validation result: mIoU 58.11, best mIoU 59.07
2024-09-09 15:31:35,745 - train.py[line:541] - INFO: Avg train time: 74.64s, avg eval time: 448.73s, left eval count: 138, ETA: 2024-09-10 11:35:20
2024-09-09 15:32:52,718 - train.py[line:384] - INFO: Epoch 363/500 Iter 100/100: lr=1.8714e-05 loss=0.0412 total_loss=0.0362
2024-09-09 15:40:21,573 - train.py[line:525] - INFO: Epoch 363 validation result: mIoU 58.6, best mIoU 59.07
2024-09-09 15:40:21,573 - train.py[line:541] - INFO: Avg train time: 75.16s, avg eval time: 448.78s, left eval count: 137, ETA: 2024-09-10 11:36:40
2024-09-09 15:41:36,884 - train.py[line:384] - INFO: Epoch 364/500 Iter 100/100: lr=1.8591e-05 loss=0.0565 total_loss=0.0356
2024-09-09 15:49:04,747 - train.py[line:525] - INFO: Epoch 364 validation result: mIoU 58.22, best mIoU 59.07
2024-09-09 15:49:04,747 - train.py[line:541] - INFO: Avg train time: 74.85s, avg eval time: 448.41s, left eval count: 136, ETA: 2024-09-10 11:35:08
2024-09-09 15:50:25,012 - train.py[line:384] - INFO: Epoch 365/500 Iter 100/100: lr=1.8467e-05 loss=0.0427 total_loss=0.0353
2024-09-09 15:57:52,879 - train.py[line:525] - INFO: Epoch 365 validation result: mIoU 58.27, best mIoU 59.07
2024-09-09 15:57:52,879 - train.py[line:541] - INFO: Avg train time: 76.56s, avg eval time: 448.19s, left eval count: 135, ETA: 2024-09-10 11:38:35
2024-09-09 15:59:09,600 - train.py[line:384] - INFO: Epoch 366/500 Iter 100/100: lr=1.8344e-05 loss=0.0314 total_loss=0.0342
2024-09-09 16:06:38,317 - train.py[line:525] - INFO: Epoch 366 validation result: mIoU 58.59, best mIoU 59.07
2024-09-09 16:06:38,318 - train.py[line:541] - INFO: Avg train time: 76.14s, avg eval time: 448.40s, left eval count: 134, ETA: 2024-09-10 11:38:07
2024-09-09 16:07:47,187 - train.py[line:384] - INFO: Epoch 367/500 Iter 100/100: lr=1.8221e-05 loss=0.0294 total_loss=0.0359
2024-09-09 16:15:15,449 - train.py[line:525] - INFO: Epoch 367 validation result: mIoU 58.76, best mIoU 59.07
2024-09-09 16:15:15,450 - train.py[line:541] - INFO: Avg train time: 72.73s, avg eval time: 448.35s, left eval count: 133, ETA: 2024-09-10 11:30:18
2024-09-09 16:16:36,206 - train.py[line:384] - INFO: Epoch 368/500 Iter 100/100: lr=1.8098e-05 loss=0.0321 total_loss=0.0357
2024-09-09 16:24:05,142 - train.py[line:525] - INFO: Epoch 368 validation result: mIoU 58.58, best mIoU 59.07
2024-09-09 16:24:05,143 - train.py[line:541] - INFO: Avg train time: 75.47s, avg eval time: 448.58s, left eval count: 132, ETA: 2024-09-10 11:36:59
2024-09-09 16:25:13,576 - train.py[line:384] - INFO: Epoch 369/500 Iter 100/100: lr=1.7974e-05 loss=0.0363 total_loss=0.0354
2024-09-09 16:32:41,438 - train.py[line:525] - INFO: Epoch 369 validation result: mIoU 58.59, best mIoU 59.07
2024-09-09 16:32:41,439 - train.py[line:541] - INFO: Avg train time: 72.20s, avg eval time: 448.29s, left eval count: 131, ETA: 2024-09-10 11:29:05
2024-09-09 16:34:01,571 - train.py[line:384] - INFO: Epoch 370/500 Iter 100/100: lr=1.7851e-05 loss=0.0261 total_loss=0.0354
2024-09-09 16:41:30,455 - train.py[line:525] - INFO: Epoch 370 validation result: mIoU 58.63, best mIoU 59.07
2024-09-09 16:41:30,456 - train.py[line:541] - INFO: Avg train time: 74.90s, avg eval time: 448.53s, left eval count: 130, ETA: 2024-09-10 11:35:36
2024-09-09 16:42:48,439 - train.py[line:384] - INFO: Epoch 371/500 Iter 100/100: lr=1.7727e-05 loss=0.0338 total_loss=0.0350
2024-09-09 16:50:15,971 - train.py[line:525] - INFO: Epoch 371 validation result: mIoU 58.46, best mIoU 59.07
2024-09-09 16:50:15,972 - train.py[line:541] - INFO: Avg train time: 75.64s, avg eval time: 448.13s, left eval count: 129, ETA: 2024-09-10 11:36:22
2024-09-09 16:51:32,098 - train.py[line:384] - INFO: Epoch 372/500 Iter 100/100: lr=1.7603e-05 loss=0.0244 total_loss=0.0353
2024-09-09 16:59:00,060 - train.py[line:525] - INFO: Epoch 372 validation result: mIoU 58.67, best mIoU 59.07
2024-09-09 16:59:00,061 - train.py[line:541] - INFO: Avg train time: 75.35s, avg eval time: 448.06s, left eval count: 128, ETA: 2024-09-10 11:35:36
2024-09-09 17:00:15,161 - train.py[line:384] - INFO: Epoch 373/500 Iter 100/100: lr=1.7480e-05 loss=0.0335 total_loss=0.0350
2024-09-09 17:07:43,599 - train.py[line:525] - INFO: Epoch 373 validation result: mIoU 58.73, best mIoU 59.07
2024-09-09 17:07:43,599 - train.py[line:541] - INFO: Avg train time: 74.88s, avg eval time: 448.21s, left eval count: 127, ETA: 2024-09-10 11:34:55
2024-09-09 17:08:54,586 - train.py[line:384] - INFO: Epoch 374/500 Iter 100/100: lr=1.7356e-05 loss=0.0297 total_loss=0.0347
2024-09-09 17:16:23,203 - train.py[line:525] - INFO: Epoch 374 validation result: mIoU 58.79, best mIoU 59.07
2024-09-09 17:16:23,204 - train.py[line:541] - INFO: Avg train time: 72.84s, avg eval time: 448.37s, left eval count: 126, ETA: 2024-09-10 11:30:56
2024-09-09 17:17:43,732 - train.py[line:384] - INFO: Epoch 375/500 Iter 100/100: lr=1.7232e-05 loss=0.0314 total_loss=0.0364
2024-09-09 17:25:12,117 - train.py[line:525] - INFO: Epoch 375 validation result: mIoU 58.66, best mIoU 59.07
2024-09-09 17:25:12,118 - train.py[line:541] - INFO: Avg train time: 75.45s, avg eval time: 448.38s, left eval count: 125, ETA: 2024-09-10 11:36:30
2024-09-09 17:26:36,577 - train.py[line:384] - INFO: Epoch 376/500 Iter 100/100: lr=1.7108e-05 loss=0.0296 total_loss=0.0386
2024-09-09 17:34:05,982 - train.py[line:525] - INFO: Epoch 376 validation result: mIoU 58.58, best mIoU 59.07
2024-09-09 17:34:05,982 - train.py[line:541] - INFO: Avg train time: 78.66s, avg eval time: 448.79s, left eval count: 124, ETA: 2024-09-10 11:44:09
2024-09-09 17:35:22,497 - train.py[line:384] - INFO: Epoch 377/500 Iter 100/100: lr=1.6983e-05 loss=0.0390 total_loss=0.0343
2024-09-09 17:42:51,477 - train.py[line:525] - INFO: Epoch 377 validation result: mIoU 58.52, best mIoU 59.07
2024-09-09 17:42:51,478 - train.py[line:541] - INFO: Avg train time: 77.32s, avg eval time: 448.87s, left eval count: 123, ETA: 2024-09-10 11:41:32
2024-09-09 17:44:09,124 - train.py[line:384] - INFO: Epoch 378/500 Iter 100/100: lr=1.6859e-05 loss=0.0402 total_loss=0.0344
2024-09-09 17:51:38,242 - train.py[line:525] - INFO: Epoch 378 validation result: mIoU 58.47, best mIoU 59.07
2024-09-09 17:51:38,243 - train.py[line:541] - INFO: Avg train time: 77.01s, avg eval time: 448.97s, left eval count: 122, ETA: 2024-09-10 11:41:07
2024-09-09 17:52:50,577 - train.py[line:384] - INFO: Epoch 379/500 Iter 100/100: lr=1.6735e-05 loss=0.0374 total_loss=0.0350
2024-09-09 18:00:18,444 - train.py[line:525] - INFO: Epoch 379 validation result: mIoU 58.57, best mIoU 59.07
2024-09-09 18:00:18,445 - train.py[line:541] - INFO: Avg train time: 74.72s, avg eval time: 448.53s, left eval count: 121, ETA: 2024-09-10 11:35:31
2024-09-09 18:01:24,764 - train.py[line:384] - INFO: Epoch 380/500 Iter 100/100: lr=1.6610e-05 loss=0.0230 total_loss=0.0344
2024-09-09 18:08:53,463 - train.py[line:525] - INFO: Epoch 380 validation result: mIoU 58.42, best mIoU 59.07
2024-09-09 18:08:53,464 - train.py[line:541] - INFO: Avg train time: 70.98s, avg eval time: 448.60s, left eval count: 120, ETA: 2024-09-10 11:28:02
2024-09-09 18:10:06,973 - train.py[line:384] - INFO: Epoch 381/500 Iter 100/100: lr=1.6486e-05 loss=0.0262 total_loss=0.0345
2024-09-09 18:17:35,149 - train.py[line:525] - INFO: Epoch 381 validation result: mIoU 58.39, best mIoU 59.07
2024-09-09 18:17:35,150 - train.py[line:541] - INFO: Avg train time: 71.54s, avg eval time: 448.43s, left eval count: 119, ETA: 2024-09-10 11:28:51
2024-09-09 18:18:43,366 - train.py[line:384] - INFO: Epoch 382/500 Iter 100/100: lr=1.6361e-05 loss=0.0296 total_loss=0.0334
2024-09-09 18:26:11,542 - train.py[line:525] - INFO: Epoch 382 validation result: mIoU 58.43, best mIoU 59.07
2024-09-09 18:26:11,543 - train.py[line:541] - INFO: Avg train time: 69.82s, avg eval time: 448.33s, left eval count: 118, ETA: 2024-09-10 11:25:13
2024-09-09 18:27:24,970 - train.py[line:384] - INFO: Epoch 383/500 Iter 100/100: lr=1.6236e-05 loss=0.0360 total_loss=0.0344
2024-09-09 18:34:53,777 - train.py[line:525] - INFO: Epoch 383 validation result: mIoU 58.48, best mIoU 59.07
2024-09-09 18:34:53,778 - train.py[line:541] - INFO: Avg train time: 70.81s, avg eval time: 448.52s, left eval count: 117, ETA: 2024-09-10 11:27:34
2024-09-09 18:36:07,536 - train.py[line:384] - INFO: Epoch 384/500 Iter 100/100: lr=1.6111e-05 loss=0.0506 total_loss=0.0350
2024-09-09 18:43:36,370 - train.py[line:525] - INFO: Epoch 384 validation result: mIoU 58.45, best mIoU 59.07
2024-09-09 18:43:36,371 - train.py[line:541] - INFO: Avg train time: 71.53s, avg eval time: 448.65s, left eval count: 116, ETA: 2024-09-10 11:29:16
2024-09-09 18:44:55,224 - train.py[line:384] - INFO: Epoch 385/500 Iter 100/100: lr=1.5986e-05 loss=0.0300 total_loss=0.0334
2024-09-09 18:52:23,465 - train.py[line:525] - INFO: Epoch 385 validation result: mIoU 58.61, best mIoU 59.07
2024-09-09 18:52:23,466 - train.py[line:541] - INFO: Avg train time: 73.97s, avg eval time: 448.48s, left eval count: 115, ETA: 2024-09-10 11:33:45
2024-09-09 18:53:34,354 - train.py[line:384] - INFO: Epoch 386/500 Iter 100/100: lr=1.5861e-05 loss=0.0405 total_loss=0.0348
2024-09-09 19:01:01,967 - train.py[line:525] - INFO: Epoch 386 validation result: mIoU 58.72, best mIoU 59.07
2024-09-09 19:01:01,967 - train.py[line:541] - INFO: Avg train time: 72.32s, avg eval time: 448.14s, left eval count: 114, ETA: 2024-09-10 11:29:53
2024-09-09 19:02:18,124 - train.py[line:384] - INFO: Epoch 387/500 Iter 100/100: lr=1.5736e-05 loss=0.0502 total_loss=0.0352
2024-09-09 19:09:47,881 - train.py[line:525] - INFO: Epoch 387 validation result: mIoU 57.89, best mIoU 59.07
2024-09-09 19:09:47,882 - train.py[line:541] - INFO: Avg train time: 73.44s, avg eval time: 448.78s, left eval count: 113, ETA: 2024-09-10 11:33:18
2024-09-09 19:11:01,695 - train.py[line:384] - INFO: Epoch 388/500 Iter 100/100: lr=1.5610e-05 loss=0.0243 total_loss=0.0358
2024-09-09 19:18:29,902 - train.py[line:525] - INFO: Epoch 388 validation result: mIoU 58.38, best mIoU 59.07
2024-09-09 19:18:29,903 - train.py[line:541] - INFO: Avg train time: 73.18s, avg eval time: 448.55s, left eval count: 112, ETA: 2024-09-10 11:32:24
2024-09-09 19:19:55,879 - train.py[line:384] - INFO: Epoch 389/500 Iter 100/100: lr=1.5485e-05 loss=0.0177 total_loss=0.0351
2024-09-09 19:27:25,017 - train.py[line:525] - INFO: Epoch 389 validation result: mIoU 57.95, best mIoU 59.07
2024-09-09 19:27:25,017 - train.py[line:541] - INFO: Avg train time: 77.75s, avg eval time: 448.79s, left eval count: 111, ETA: 2024-09-10 11:41:30
2024-09-09 19:28:35,661 - train.py[line:384] - INFO: Epoch 390/500 Iter 100/100: lr=1.5359e-05 loss=0.0275 total_loss=0.0339
2024-09-09 19:36:04,157 - train.py[line:525] - INFO: Epoch 390 validation result: mIoU 58.34, best mIoU 59.07
2024-09-09 19:36:04,158 - train.py[line:541] - INFO: Avg train time: 74.48s, avg eval time: 448.67s, left eval count: 110, ETA: 2024-09-10 11:35:10
2024-09-09 19:37:16,049 - train.py[line:384] - INFO: Epoch 391/500 Iter 100/100: lr=1.5233e-05 loss=0.0378 total_loss=0.0342
2024-09-09 19:44:45,231 - train.py[line:525] - INFO: Epoch 391 validation result: mIoU 58.64, best mIoU 59.07
2024-09-09 19:44:45,232 - train.py[line:541] - INFO: Avg train time: 72.91s, avg eval time: 448.88s, left eval count: 109, ETA: 2024-09-10 11:32:39
2024-09-09 19:46:07,337 - train.py[line:384] - INFO: Epoch 392/500 Iter 100/100: lr=1.5108e-05 loss=0.0587 total_loss=0.0346
2024-09-09 19:53:36,129 - train.py[line:525] - INFO: Epoch 392 validation result: mIoU 58.67, best mIoU 59.07
2024-09-09 19:53:36,130 - train.py[line:541] - INFO: Avg train time: 76.05s, avg eval time: 448.84s, left eval count: 108, ETA: 2024-09-10 11:38:23
2024-09-09 19:54:53,783 - train.py[line:384] - INFO: Epoch 393/500 Iter 100/100: lr=1.4982e-05 loss=0.0500 total_loss=0.0337
2024-09-09 20:02:22,480 - train.py[line:525] - INFO: Epoch 393 validation result: mIoU 58.56, best mIoU 59.07
2024-09-09 20:02:22,481 - train.py[line:541] - INFO: Avg train time: 76.14s, avg eval time: 448.78s, left eval count: 107, ETA: 2024-09-10 11:38:29
2024-09-09 20:03:43,376 - train.py[line:384] - INFO: Epoch 394/500 Iter 100/100: lr=1.4856e-05 loss=0.0421 total_loss=0.0333
2024-09-09 20:11:12,217 - train.py[line:525] - INFO: Epoch 394 validation result: mIoU 58.46, best mIoU 59.07
2024-09-09 20:11:12,218 - train.py[line:541] - INFO: Avg train time: 77.48s, avg eval time: 448.81s, left eval count: 106, ETA: 2024-09-10 11:40:58
2024-09-09 20:12:25,228 - train.py[line:384] - INFO: Epoch 395/500 Iter 100/100: lr=1.4729e-05 loss=0.0480 total_loss=0.0338
2024-09-09 20:19:54,169 - train.py[line:525] - INFO: Epoch 395 validation result: mIoU 58.83, best mIoU 59.07
2024-09-09 20:19:54,170 - train.py[line:541] - INFO: Avg train time: 75.10s, avg eval time: 448.86s, left eval count: 105, ETA: 2024-09-10 11:36:50
2024-09-09 20:21:12,499 - train.py[line:384] - INFO: Epoch 396/500 Iter 100/100: lr=1.4603e-05 loss=0.0245 total_loss=0.0345
2024-09-09 20:28:41,649 - train.py[line:525] - INFO: Epoch 396 validation result: mIoU 58.73, best mIoU 59.07
2024-09-09 20:28:41,650 - train.py[line:541] - INFO: Avg train time: 75.86s, avg eval time: 448.98s, left eval count: 104, ETA: 2024-09-10 11:38:24
2024-09-09 20:29:57,649 - train.py[line:384] - INFO: Epoch 397/500 Iter 100/100: lr=1.4477e-05 loss=0.0257 total_loss=0.0348
2024-09-09 20:37:26,179 - train.py[line:525] - INFO: Epoch 397 validation result: mIoU 58.72, best mIoU 59.07
2024-09-09 20:37:26,179 - train.py[line:541] - INFO: Avg train time: 75.41s, avg eval time: 448.80s, left eval count: 103, ETA: 2024-09-10 11:37:19
2024-09-09 20:38:50,125 - train.py[line:384] - INFO: Epoch 398/500 Iter 100/100: lr=1.4350e-05 loss=0.0304 total_loss=0.0329
2024-09-09 20:46:18,724 - train.py[line:525] - INFO: Epoch 398 validation result: mIoU 58.95, best mIoU 59.07
2024-09-09 20:46:18,725 - train.py[line:541] - INFO: Avg train time: 78.41s, avg eval time: 448.72s, left eval count: 102, ETA: 2024-09-10 11:42:25
2024-09-09 20:47:27,299 - train.py[line:384] - INFO: Epoch 399/500 Iter 100/100: lr=1.4223e-05 loss=0.0323 total_loss=0.0328
2024-09-09 20:54:56,792 - train.py[line:525] - INFO: Epoch 399 validation result: mIoU 58.83, best mIoU 59.07
2024-09-09 20:54:56,792 - train.py[line:541] - INFO: Avg train time: 74.04s, avg eval time: 449.03s, left eval count: 101, ETA: 2024-09-10 11:35:26
2024-09-09 20:56:04,622 - train.py[line:384] - INFO: Epoch 400/500 Iter 100/100: lr=1.4097e-05 loss=0.0284 total_loss=0.0334
2024-09-09 21:03:32,988 - train.py[line:525] - INFO: Epoch 400 validation result: mIoU 58.89, best mIoU 59.07
2024-09-09 21:03:32,989 - train.py[line:541] - INFO: Avg train time: 71.12s, avg eval time: 448.76s, left eval count: 100, ETA: 2024-09-10 11:30:01
2024-09-09 21:04:47,921 - train.py[line:384] - INFO: Epoch 401/500 Iter 100/100: lr=1.3970e-05 loss=0.0577 total_loss=0.0327
2024-09-09 21:12:16,790 - train.py[line:525] - INFO: Epoch 401 validation result: mIoU 58.86, best mIoU 59.07
2024-09-09 21:12:16,791 - train.py[line:541] - INFO: Avg train time: 72.07s, avg eval time: 448.81s, left eval count: 99, ETA: 2024-09-10 11:31:43
2024-09-09 21:13:34,498 - train.py[line:384] - INFO: Epoch 402/500 Iter 100/100: lr=1.3843e-05 loss=0.0300 total_loss=0.0335
2024-09-09 21:21:02,996 - train.py[line:525] - INFO: Epoch 402 validation result: mIoU 58.86, best mIoU 59.07
2024-09-09 21:21:02,997 - train.py[line:541] - INFO: Avg train time: 73.80s, avg eval time: 448.68s, left eval count: 98, ETA: 2024-09-10 11:34:26
2024-09-09 21:22:23,384 - train.py[line:384] - INFO: Epoch 403/500 Iter 100/100: lr=1.3716e-05 loss=0.0300 total_loss=0.0335
2024-09-09 21:29:51,909 - train.py[line:525] - INFO: Epoch 403 validation result: mIoU 58.74, best mIoU 59.07
2024-09-09 21:29:51,910 - train.py[line:541] - INFO: Avg train time: 75.91s, avg eval time: 448.62s, left eval count: 97, ETA: 2024-09-10 11:37:51
2024-09-09 21:30:56,182 - train.py[line:384] - INFO: Epoch 404/500 Iter 100/100: lr=1.3588e-05 loss=0.0282 total_loss=0.0327
2024-09-09 21:38:24,281 - train.py[line:525] - INFO: Epoch 404 validation result: mIoU 58.38, best mIoU 59.07
2024-09-09 21:38:24,282 - train.py[line:541] - INFO: Avg train time: 70.73s, avg eval time: 448.41s, left eval count: 96, ETA: 2024-09-10 11:29:02
2024-09-09 21:39:42,038 - train.py[line:384] - INFO: Epoch 405/500 Iter 100/100: lr=1.3461e-05 loss=0.0300 total_loss=0.0332
2024-09-09 21:47:10,205 - train.py[line:525] - INFO: Epoch 405 validation result: mIoU 58.69, best mIoU 59.07
2024-09-09 21:47:10,206 - train.py[line:541] - INFO: Avg train time: 73.02s, avg eval time: 448.31s, left eval count: 95, ETA: 2024-09-10 11:32:36
2024-09-09 21:48:18,640 - train.py[line:384] - INFO: Epoch 406/500 Iter 100/100: lr=1.3333e-05 loss=0.0325 total_loss=0.0325
2024-09-09 21:55:47,042 - train.py[line:525] - INFO: Epoch 406 validation result: mIoU 58.72, best mIoU 59.07
2024-09-09 21:55:47,043 - train.py[line:541] - INFO: Avg train time: 70.62s, avg eval time: 448.35s, left eval count: 94, ETA: 2024-09-10 11:28:50
2024-09-09 21:57:09,475 - train.py[line:384] - INFO: Epoch 407/500 Iter 100/100: lr=1.3206e-05 loss=0.0352 total_loss=0.0330
2024-09-09 22:04:39,446 - train.py[line:525] - INFO: Epoch 407 validation result: mIoU 58.76, best mIoU 59.07
2024-09-09 22:04:39,447 - train.py[line:541] - INFO: Avg train time: 74.97s, avg eval time: 449.00s, left eval count: 93, ETA: 2024-09-10 11:36:48
2024-09-09 22:05:57,729 - train.py[line:384] - INFO: Epoch 408/500 Iter 100/100: lr=1.3078e-05 loss=0.0325 total_loss=0.0325
2024-09-09 22:13:27,300 - train.py[line:525] - INFO: Epoch 408 validation result: mIoU 58.89, best mIoU 59.07
2024-09-09 22:13:27,301 - train.py[line:541] - INFO: Avg train time: 75.87s, avg eval time: 449.23s, left eval count: 92, ETA: 2024-09-10 11:38:36
2024-09-09 22:14:39,429 - train.py[line:384] - INFO: Epoch 409/500 Iter 100/100: lr=1.2950e-05 loss=0.0215 total_loss=0.0321
2024-09-09 22:22:08,431 - train.py[line:525] - INFO: Epoch 409 validation result: mIoU 58.74, best mIoU 59.07
2024-09-09 22:22:08,432 - train.py[line:541] - INFO: Avg train time: 74.02s, avg eval time: 449.14s, left eval count: 91, ETA: 2024-09-10 11:35:35
2024-09-09 22:23:24,264 - train.py[line:384] - INFO: Epoch 410/500 Iter 100/100: lr=1.2822e-05 loss=0.0297 total_loss=0.0326
2024-09-09 22:30:52,804 - train.py[line:525] - INFO: Epoch 410 validation result: mIoU 58.63, best mIoU 59.07
2024-09-09 22:30:52,805 - train.py[line:541] - INFO: Avg train time: 74.28s, avg eval time: 448.90s, left eval count: 90, ETA: 2024-09-10 11:35:38
2024-09-09 22:32:03,097 - train.py[line:384] - INFO: Epoch 411/500 Iter 100/100: lr=1.2693e-05 loss=0.0244 total_loss=0.0326
2024-09-09 22:39:31,561 - train.py[line:525] - INFO: Epoch 411 validation result: mIoU 58.72, best mIoU 59.07
2024-09-09 22:39:31,562 - train.py[line:541] - INFO: Avg train time: 72.18s, avg eval time: 448.72s, left eval count: 89, ETA: 2024-09-10 11:32:11
2024-09-09 22:40:54,288 - train.py[line:384] - INFO: Epoch 412/500 Iter 100/100: lr=1.2565e-05 loss=0.0250 total_loss=0.0321
2024-09-09 22:48:23,751 - train.py[line:525] - INFO: Epoch 412 validation result: mIoU 58.67, best mIoU 59.07
2024-09-09 22:48:23,752 - train.py[line:541] - INFO: Avg train time: 75.86s, avg eval time: 449.02s, left eval count: 88, ETA: 2024-09-10 11:38:12
2024-09-09 22:49:32,151 - train.py[line:384] - INFO: Epoch 413/500 Iter 100/100: lr=1.2436e-05 loss=0.0321 total_loss=0.0332
2024-09-09 22:57:01,017 - train.py[line:525] - INFO: Epoch 413 validation result: mIoU 58.73, best mIoU 59.07
2024-09-09 22:57:01,018 - train.py[line:541] - INFO: Avg train time: 72.30s, avg eval time: 448.96s, left eval count: 87, ETA: 2024-09-10 11:32:50
2024-09-09 22:58:18,900 - train.py[line:384] - INFO: Epoch 414/500 Iter 100/100: lr=1.2308e-05 loss=0.0269 total_loss=0.0334
2024-09-09 23:05:48,072 - train.py[line:525] - INFO: Epoch 414 validation result: mIoU 58.68, best mIoU 59.07
2024-09-09 23:05:48,073 - train.py[line:541] - INFO: Avg train time: 74.02s, avg eval time: 449.04s, left eval count: 86, ETA: 2024-09-10 11:35:31
2024-09-09 23:07:02,051 - train.py[line:384] - INFO: Epoch 415/500 Iter 100/100: lr=1.2179e-05 loss=0.0290 total_loss=0.0329
2024-09-09 23:14:31,641 - train.py[line:525] - INFO: Epoch 415 validation result: mIoU 58.68, best mIoU 59.07
2024-09-09 23:14:31,642 - train.py[line:541] - INFO: Avg train time: 73.51s, avg eval time: 449.26s, left eval count: 85, ETA: 2024-09-10 11:35:06
2024-09-09 23:15:39,248 - train.py[line:384] - INFO: Epoch 416/500 Iter 100/100: lr=1.2050e-05 loss=0.0350 total_loss=0.0329
2024-09-09 23:23:08,522 - train.py[line:525] - INFO: Epoch 416 validation result: mIoU 58.64, best mIoU 59.07
2024-09-09 23:23:08,522 - train.py[line:541] - INFO: Avg train time: 70.64s, avg eval time: 449.27s, left eval count: 84, ETA: 2024-09-10 11:31:00
2024-09-09 23:24:25,772 - train.py[line:384] - INFO: Epoch 417/500 Iter 100/100: lr=1.1921e-05 loss=0.0250 total_loss=0.0328
2024-09-09 23:31:54,996 - train.py[line:525] - INFO: Epoch 417 validation result: mIoU 58.79, best mIoU 59.07
2024-09-09 23:31:54,997 - train.py[line:541] - INFO: Avg train time: 72.77s, avg eval time: 449.25s, left eval count: 83, ETA: 2024-09-10 11:34:02
2024-09-09 23:33:12,068 - train.py[line:384] - INFO: Epoch 418/500 Iter 100/100: lr=1.1791e-05 loss=0.0488 total_loss=0.0325
2024-09-09 23:40:42,381 - train.py[line:525] - INFO: Epoch 418 validation result: mIoU 58.65, best mIoU 59.07
2024-09-09 23:40:42,382 - train.py[line:541] - INFO: Avg train time: 73.94s, avg eval time: 449.68s, left eval count: 82, ETA: 2024-09-10 11:36:18
2024-09-09 23:42:06,106 - train.py[line:384] - INFO: Epoch 419/500 Iter 100/100: lr=1.1662e-05 loss=0.0328 total_loss=0.0324
2024-09-09 23:49:35,572 - train.py[line:525] - INFO: Epoch 419 validation result: mIoU 58.36, best mIoU 59.07
2024-09-09 23:49:35,573 - train.py[line:541] - INFO: Avg train time: 77.36s, avg eval time: 449.59s, left eval count: 81, ETA: 2024-09-10 11:40:58
2024-09-09 23:50:46,942 - train.py[line:384] - INFO: Epoch 420/500 Iter 100/100: lr=1.1532e-05 loss=0.0401 total_loss=0.0334
2024-09-09 23:58:16,843 - train.py[line:525] - INFO: Epoch 420 validation result: mIoU 58.39, best mIoU 59.07
2024-09-09 23:58:16,843 - train.py[line:541] - INFO: Avg train time: 74.52s, avg eval time: 449.72s, left eval count: 80, ETA: 2024-09-10 11:37:15
2024-09-09 23:59:35,148 - train.py[line:384] - INFO: Epoch 421/500 Iter 100/100: lr=1.1402e-05 loss=0.0382 total_loss=0.0319
2024-09-10 00:07:04,746 - train.py[line:525] - INFO: Epoch 421 validation result: mIoU 58.58, best mIoU 59.07
2024-09-10 00:07:04,748 - train.py[line:541] - INFO: Avg train time: 75.50s, avg eval time: 449.67s, left eval count: 79, ETA: 2024-09-10 11:38:32
2024-09-10 00:08:19,981 - train.py[line:384] - INFO: Epoch 422/500 Iter 100/100: lr=1.1272e-05 loss=0.0279 total_loss=0.0316
2024-09-10 00:15:50,452 - train.py[line:525] - INFO: Epoch 422 validation result: mIoU 58.57, best mIoU 59.07
2024-09-10 00:15:50,453 - train.py[line:541] - INFO: Avg train time: 74.74s, avg eval time: 449.99s, left eval count: 78, ETA: 2024-09-10 11:37:59
2024-09-10 00:17:07,098 - train.py[line:384] - INFO: Epoch 423/500 Iter 100/100: lr=1.1142e-05 loss=0.0343 total_loss=0.0318
2024-09-10 00:24:37,170 - train.py[line:525] - INFO: Epoch 423 validation result: mIoU 58.67, best mIoU 59.07
2024-09-10 00:24:37,171 - train.py[line:541] - INFO: Avg train time: 74.95s, avg eval time: 450.02s, left eval count: 77, ETA: 2024-09-10 11:38:20
2024-09-10 00:25:53,245 - train.py[line:384] - INFO: Epoch 424/500 Iter 100/100: lr=1.1012e-05 loss=0.0317 total_loss=0.0342
2024-09-10 00:33:22,491 - train.py[line:525] - INFO: Epoch 424 validation result: mIoU 58.5, best mIoU 59.07
2024-09-10 00:33:22,492 - train.py[line:541] - INFO: Avg train time: 74.99s, avg eval time: 449.71s, left eval count: 76, ETA: 2024-09-10 11:38:00
2024-09-10 00:34:30,025 - train.py[line:384] - INFO: Epoch 425/500 Iter 100/100: lr=1.0881e-05 loss=0.0485 total_loss=0.0319
2024-09-10 00:42:00,575 - train.py[line:525] - INFO: Epoch 425 validation result: mIoU 58.57, best mIoU 59.07
2024-09-10 00:42:00,575 - train.py[line:541] - INFO: Avg train time: 71.59s, avg eval time: 450.05s, left eval count: 75, ETA: 2024-09-10 11:34:03
2024-09-10 00:43:17,763 - train.py[line:384] - INFO: Epoch 426/500 Iter 100/100: lr=1.0751e-05 loss=0.0287 total_loss=0.0321
2024-09-10 00:50:47,325 - train.py[line:525] - INFO: Epoch 426 validation result: mIoU 58.63, best mIoU 59.07
2024-09-10 00:50:47,325 - train.py[line:541] - INFO: Avg train time: 73.43s, avg eval time: 449.85s, left eval count: 74, ETA: 2024-09-10 11:36:09
2024-09-10 00:52:07,999 - train.py[line:384] - INFO: Epoch 427/500 Iter 100/100: lr=1.0620e-05 loss=0.0249 total_loss=0.0318
2024-09-10 00:59:37,993 - train.py[line:525] - INFO: Epoch 427 validation result: mIoU 58.71, best mIoU 59.07
2024-09-10 00:59:37,994 - train.py[line:541] - INFO: Avg train time: 75.82s, avg eval time: 449.91s, left eval count: 73, ETA: 2024-09-10 11:39:16
2024-09-10 01:00:51,363 - train.py[line:384] - INFO: Epoch 428/500 Iter 100/100: lr=1.0489e-05 loss=0.0442 total_loss=0.0317
2024-09-10 01:08:22,320 - train.py[line:525] - INFO: Epoch 428 validation result: mIoU 58.78, best mIoU 59.07
2024-09-10 01:08:22,320 - train.py[line:541] - INFO: Avg train time: 74.55s, avg eval time: 450.33s, left eval count: 72, ETA: 2024-09-10 11:38:13
2024-09-10 01:09:41,601 - train.py[line:384] - INFO: Epoch 429/500 Iter 100/100: lr=1.0358e-05 loss=0.0320 total_loss=0.0322
2024-09-10 01:17:11,205 - train.py[line:525] - INFO: Epoch 429 validation result: mIoU 58.67, best mIoU 59.07
2024-09-10 01:17:11,205 - train.py[line:541] - INFO: Avg train time: 75.89s, avg eval time: 450.04s, left eval count: 71, ETA: 2024-09-10 11:39:31
2024-09-10 01:18:32,157 - train.py[line:384] - INFO: Epoch 430/500 Iter 100/100: lr=1.0226e-05 loss=0.0348 total_loss=0.0316
2024-09-10 01:26:01,273 - train.py[line:525] - INFO: Epoch 430 validation result: mIoU 58.66, best mIoU 59.07
2024-09-10 01:26:01,274 - train.py[line:541] - INFO: Avg train time: 77.52s, avg eval time: 449.67s, left eval count: 70, ETA: 2024-09-10 11:41:04
2024-09-10 01:27:10,628 - train.py[line:384] - INFO: Epoch 431/500 Iter 100/100: lr=1.0095e-05 loss=0.0323 total_loss=0.0316
2024-09-10 01:34:40,509 - train.py[line:525] - INFO: Epoch 431 validation result: mIoU 58.82, best mIoU 59.07
2024-09-10 01:34:40,509 - train.py[line:541] - INFO: Avg train time: 73.77s, avg eval time: 449.75s, left eval count: 69, ETA: 2024-09-10 11:36:43
2024-09-10 01:35:57,700 - train.py[line:384] - INFO: Epoch 432/500 Iter 100/100: lr=9.9631e-06 loss=0.0396 total_loss=0.0322
2024-09-10 01:43:27,123 - train.py[line:525] - INFO: Epoch 432 validation result: mIoU 58.61, best mIoU 59.07
2024-09-10 01:43:27,124 - train.py[line:541] - INFO: Avg train time: 74.62s, avg eval time: 449.62s, left eval count: 68, ETA: 2024-09-10 11:37:35
2024-09-10 01:44:53,292 - train.py[line:384] - INFO: Epoch 433/500 Iter 100/100: lr=9.8311e-06 loss=0.0210 total_loss=0.0313
2024-09-10 01:52:22,415 - train.py[line:525] - INFO: Epoch 433 validation result: mIoU 58.61, best mIoU 59.07
2024-09-10 01:52:22,416 - train.py[line:541] - INFO: Avg train time: 78.73s, avg eval time: 449.42s, left eval count: 67, ETA: 2024-09-10 11:42:08
2024-09-10 01:53:44,672 - train.py[line:384] - INFO: Epoch 434/500 Iter 100/100: lr=9.6990e-06 loss=0.0290 total_loss=0.0312
2024-09-10 02:01:13,247 - train.py[line:525] - INFO: Epoch 434 validation result: mIoU 58.67, best mIoU 59.07
2024-09-10 02:01:13,248 - train.py[line:541] - INFO: Avg train time: 79.66s, avg eval time: 449.08s, left eval count: 66, ETA: 2024-09-10 11:42:50
2024-09-10 02:02:30,218 - train.py[line:384] - INFO: Epoch 435/500 Iter 100/100: lr=9.5667e-06 loss=0.0303 total_loss=0.0313
2024-09-10 02:09:59,780 - train.py[line:525] - INFO: Epoch 435 validation result: mIoU 58.59, best mIoU 59.07
2024-09-10 02:09:59,781 - train.py[line:541] - INFO: Avg train time: 78.21s, avg eval time: 449.28s, left eval count: 65, ETA: 2024-09-10 11:41:26
2024-09-10 02:11:17,255 - train.py[line:384] - INFO: Epoch 436/500 Iter 100/100: lr=9.4341e-06 loss=0.0395 total_loss=0.0319
2024-09-10 02:18:45,556 - train.py[line:525] - INFO: Epoch 436 validation result: mIoU 58.66, best mIoU 59.07
2024-09-10 02:18:45,557 - train.py[line:541] - INFO: Avg train time: 77.47s, avg eval time: 448.89s, left eval count: 64, ETA: 2024-09-10 11:40:12
2024-09-10 02:20:02,884 - train.py[line:384] - INFO: Epoch 437/500 Iter 100/100: lr=9.3014e-06 loss=0.0233 total_loss=0.0312
2024-09-10 02:27:32,057 - train.py[line:525] - INFO: Epoch 437 validation result: mIoU 58.72, best mIoU 59.07
2024-09-10 02:27:32,057 - train.py[line:541] - INFO: Avg train time: 76.96s, avg eval time: 449.00s, left eval count: 63, ETA: 2024-09-10 11:39:47
2024-09-10 02:28:46,364 - train.py[line:384] - INFO: Epoch 438/500 Iter 100/100: lr=9.1684e-06 loss=0.0202 total_loss=0.0318
2024-09-10 02:36:15,510 - train.py[line:525] - INFO: Epoch 438 validation result: mIoU 58.69, best mIoU 59.07
2024-09-10 02:36:15,511 - train.py[line:541] - INFO: Avg train time: 75.52s, avg eval time: 449.06s, left eval count: 62, ETA: 2024-09-10 11:38:19
2024-09-10 02:37:22,028 - train.py[line:384] - INFO: Epoch 439/500 Iter 100/100: lr=9.0352e-06 loss=0.0246 total_loss=0.0309
2024-09-10 02:44:50,805 - train.py[line:525] - INFO: Epoch 439 validation result: mIoU 58.81, best mIoU 59.07
2024-09-10 02:44:50,806 - train.py[line:541] - INFO: Avg train time: 71.37s, avg eval time: 448.95s, left eval count: 61, ETA: 2024-09-10 11:33:49
2024-09-10 02:46:11,673 - train.py[line:384] - INFO: Epoch 440/500 Iter 100/100: lr=8.9018e-06 loss=0.0218 total_loss=0.0316
2024-09-10 02:53:39,701 - train.py[line:525] - INFO: Epoch 440 validation result: mIoU 58.84, best mIoU 59.07
2024-09-10 02:53:39,701 - train.py[line:541] - INFO: Avg train time: 74.69s, avg eval time: 448.58s, left eval count: 60, ETA: 2024-09-10 11:36:55
2024-09-10 02:54:54,607 - train.py[line:384] - INFO: Epoch 441/500 Iter 100/100: lr=8.7682e-06 loss=0.0326 total_loss=0.0306
2024-09-10 03:02:22,302 - train.py[line:525] - INFO: Epoch 441 validation result: mIoU 58.86, best mIoU 59.07
2024-09-10 03:02:22,303 - train.py[line:541] - INFO: Avg train time: 74.30s, avg eval time: 448.23s, left eval count: 59, ETA: 2024-09-10 11:36:11
2024-09-10 03:03:47,033 - train.py[line:384] - INFO: Epoch 442/500 Iter 100/100: lr=8.6344e-06 loss=0.0249 total_loss=0.0313
2024-09-10 03:11:15,258 - train.py[line:525] - INFO: Epoch 442 validation result: mIoU 58.86, best mIoU 59.07
2024-09-10 03:11:15,259 - train.py[line:541] - INFO: Avg train time: 78.03s, avg eval time: 448.23s, left eval count: 58, ETA: 2024-09-10 11:39:58
2024-09-10 03:12:27,576 - train.py[line:384] - INFO: Epoch 443/500 Iter 100/100: lr=8.5003e-06 loss=0.0516 total_loss=0.0313
2024-09-10 03:19:55,170 - train.py[line:525] - INFO: Epoch 443 validation result: mIoU 58.75, best mIoU 59.07
2024-09-10 03:19:55,171 - train.py[line:541] - INFO: Avg train time: 75.21s, avg eval time: 447.97s, left eval count: 57, ETA: 2024-09-10 11:36:56
2024-09-10 03:21:05,663 - train.py[line:384] - INFO: Epoch 444/500 Iter 100/100: lr=8.3660e-06 loss=0.0289 total_loss=0.0318
2024-09-10 03:28:33,887 - train.py[line:525] - INFO: Epoch 444 validation result: mIoU 58.82, best mIoU 59.07
2024-09-10 03:28:33,888 - train.py[line:541] - INFO: Avg train time: 72.79s, avg eval time: 448.07s, left eval count: 56, ETA: 2024-09-10 11:34:42
2024-09-10 03:29:56,089 - train.py[line:384] - INFO: Epoch 445/500 Iter 100/100: lr=8.2314e-06 loss=0.0265 total_loss=0.0311
2024-09-10 03:37:25,805 - train.py[line:525] - INFO: Epoch 445 validation result: mIoU 58.8, best mIoU 59.07
2024-09-10 03:37:25,806 - train.py[line:541] - INFO: Avg train time: 76.06s, avg eval time: 448.73s, left eval count: 55, ETA: 2024-09-10 11:38:29
2024-09-10 03:38:39,765 - train.py[line:384] - INFO: Epoch 446/500 Iter 100/100: lr=8.0966e-06 loss=0.0271 total_loss=0.0305
2024-09-10 03:46:08,672 - train.py[line:525] - INFO: Epoch 446 validation result: mIoU 58.82, best mIoU 59.07
2024-09-10 03:46:08,673 - train.py[line:541] - INFO: Avg train time: 74.75s, avg eval time: 448.80s, left eval count: 54, ETA: 2024-09-10 11:37:20
2024-09-10 03:47:33,362 - train.py[line:384] - INFO: Epoch 447/500 Iter 100/100: lr=7.9616e-06 loss=0.0268 total_loss=0.0312
2024-09-10 03:55:01,961 - train.py[line:525] - INFO: Epoch 447 validation result: mIoU 58.91, best mIoU 59.07
2024-09-10 03:55:01,962 - train.py[line:541] - INFO: Avg train time: 78.23s, avg eval time: 448.72s, left eval count: 53, ETA: 2024-09-10 11:40:30
2024-09-10 03:56:13,585 - train.py[line:384] - INFO: Epoch 448/500 Iter 100/100: lr=7.8263e-06 loss=0.0425 total_loss=0.0314
2024-09-10 04:03:42,896 - train.py[line:525] - INFO: Epoch 448 validation result: mIoU 58.88, best mIoU 59.07
2024-09-10 04:03:42,897 - train.py[line:541] - INFO: Avg train time: 75.13s, avg eval time: 448.96s, left eval count: 52, ETA: 2024-09-10 11:37:55
2024-09-10 04:05:07,558 - train.py[line:384] - INFO: Epoch 449/500 Iter 100/100: lr=7.6907e-06 loss=0.0217 total_loss=0.0311
2024-09-10 04:12:35,726 - train.py[line:525] - INFO: Epoch 449 validation result: mIoU 58.96, best mIoU 59.07
2024-09-10 04:12:35,727 - train.py[line:541] - INFO: Avg train time: 78.50s, avg eval time: 448.64s, left eval count: 51, ETA: 2024-09-10 11:40:39
2024-09-10 04:14:02,317 - train.py[line:384] - INFO: Epoch 450/500 Iter 100/100: lr=7.5549e-06 loss=0.0296 total_loss=0.0315
2024-09-10 04:21:31,638 - train.py[line:525] - INFO: Epoch 450 validation result: mIoU 58.75, best mIoU 59.07
2024-09-10 04:21:31,639 - train.py[line:541] - INFO: Avg train time: 81.23s, avg eval time: 448.91s, left eval count: 50, ETA: 2024-09-10 11:43:18
2024-09-10 04:22:52,250 - train.py[line:384] - INFO: Epoch 451/500 Iter 100/100: lr=7.4188e-06 loss=0.0250 total_loss=0.0306
2024-09-10 04:30:21,519 - train.py[line:525] - INFO: Epoch 451 validation result: mIoU 58.83, best mIoU 59.07
2024-09-10 04:30:21,520 - train.py[line:541] - INFO: Avg train time: 80.48s, avg eval time: 449.06s, left eval count: 49, ETA: 2024-09-10 11:42:48
2024-09-10 04:31:31,383 - train.py[line:384] - INFO: Epoch 452/500 Iter 100/100: lr=7.2824e-06 loss=0.0349 total_loss=0.0301
2024-09-10 04:39:00,958 - train.py[line:525] - INFO: Epoch 452 validation result: mIoU 58.92, best mIoU 59.07
2024-09-10 04:39:00,959 - train.py[line:541] - INFO: Avg train time: 75.75s, avg eval time: 449.26s, left eval count: 48, ETA: 2024-09-10 11:39:01
2024-09-10 04:40:19,984 - train.py[line:384] - INFO: Epoch 453/500 Iter 100/100: lr=7.1458e-06 loss=0.0407 total_loss=0.0305
2024-09-10 04:47:48,625 - train.py[line:525] - INFO: Epoch 453 validation result: mIoU 58.85, best mIoU 59.07
2024-09-10 04:47:48,626 - train.py[line:541] - INFO: Avg train time: 76.60s, avg eval time: 449.01s, left eval count: 47, ETA: 2024-09-10 11:39:32
2024-09-10 04:49:06,937 - train.py[line:384] - INFO: Epoch 454/500 Iter 100/100: lr=7.0088e-06 loss=0.0141 total_loss=0.0310
2024-09-10 04:56:35,281 - train.py[line:525] - INFO: Epoch 454 validation result: mIoU 58.82, best mIoU 59.07
2024-09-10 04:56:35,282 - train.py[line:541] - INFO: Avg train time: 76.81s, avg eval time: 448.75s, left eval count: 46, ETA: 2024-09-10 11:39:31
2024-09-10 04:57:58,690 - train.py[line:384] - INFO: Epoch 455/500 Iter 100/100: lr=6.8716e-06 loss=0.0390 total_loss=0.0307
2024-09-10 05:05:27,415 - train.py[line:525] - INFO: Epoch 455 validation result: mIoU 58.75, best mIoU 59.07
2024-09-10 05:05:27,415 - train.py[line:541] - INFO: Avg train time: 79.08s, avg eval time: 448.74s, left eval count: 45, ETA: 2024-09-10 11:41:19
2024-09-10 05:06:46,204 - train.py[line:384] - INFO: Epoch 456/500 Iter 100/100: lr=6.7340e-06 loss=0.0327 total_loss=0.0315
2024-09-10 05:14:15,090 - train.py[line:525] - INFO: Epoch 456 validation result: mIoU 58.72, best mIoU 59.07
2024-09-10 05:14:15,090 - train.py[line:541] - INFO: Avg train time: 78.44s, avg eval time: 448.80s, left eval count: 44, ETA: 2024-09-10 11:40:53
2024-09-10 05:15:33,016 - train.py[line:384] - INFO: Epoch 457/500 Iter 100/100: lr=6.5962e-06 loss=0.0301 total_loss=0.0308
2024-09-10 05:23:01,121 - train.py[line:525] - INFO: Epoch 457 validation result: mIoU 58.76, best mIoU 59.07
2024-09-10 05:23:01,121 - train.py[line:541] - INFO: Avg train time: 77.80s, avg eval time: 448.52s, left eval count: 43, ETA: 2024-09-10 11:40:12
2024-09-10 05:24:22,168 - train.py[line:384] - INFO: Epoch 458/500 Iter 100/100: lr=6.4580e-06 loss=0.0251 total_loss=0.0303
2024-09-10 05:31:51,123 - train.py[line:525] - INFO: Epoch 458 validation result: mIoU 58.73, best mIoU 59.07
2024-09-10 05:31:51,124 - train.py[line:541] - INFO: Avg train time: 78.63s, avg eval time: 448.69s, left eval count: 42, ETA: 2024-09-10 11:40:58
2024-09-10 05:33:03,521 - train.py[line:384] - INFO: Epoch 459/500 Iter 100/100: lr=6.3194e-06 loss=0.0259 total_loss=0.0310
2024-09-10 05:40:31,658 - train.py[line:525] - INFO: Epoch 459 validation result: mIoU 58.86, best mIoU 59.07
2024-09-10 05:40:31,659 - train.py[line:541] - INFO: Avg train time: 75.66s, avg eval time: 448.47s, left eval count: 41, ETA: 2024-09-10 11:38:41
2024-09-10 05:41:51,865 - train.py[line:384] - INFO: Epoch 460/500 Iter 100/100: lr=6.1806e-06 loss=0.0223 total_loss=0.0300
2024-09-10 05:49:20,585 - train.py[line:525] - INFO: Epoch 460 validation result: mIoU 58.67, best mIoU 59.07
2024-09-10 05:49:20,586 - train.py[line:541] - INFO: Avg train time: 76.98s, avg eval time: 448.57s, left eval count: 40, ETA: 2024-09-10 11:39:42
2024-09-10 05:50:38,276 - train.py[line:384] - INFO: Epoch 461/500 Iter 100/100: lr=6.0414e-06 loss=0.0312 total_loss=0.0304
2024-09-10 05:58:07,119 - train.py[line:525] - INFO: Epoch 461 validation result: mIoU 58.64, best mIoU 59.07
2024-09-10 05:58:07,120 - train.py[line:541] - INFO: Avg train time: 76.89s, avg eval time: 448.68s, left eval count: 39, ETA: 2024-09-10 11:39:44
2024-09-10 05:59:19,830 - train.py[line:384] - INFO: Epoch 462/500 Iter 100/100: lr=5.9018e-06 loss=0.0307 total_loss=0.0299
2024-09-10 06:06:48,967 - train.py[line:525] - INFO: Epoch 462 validation result: mIoU 58.6, best mIoU 59.07
2024-09-10 06:06:48,968 - train.py[line:541] - INFO: Avg train time: 74.71s, avg eval time: 448.86s, left eval count: 38, ETA: 2024-09-10 11:38:24
2024-09-10 06:08:05,237 - train.py[line:384] - INFO: Epoch 463/500 Iter 100/100: lr=5.7619e-06 loss=0.0233 total_loss=0.0304
2024-09-10 06:15:34,609 - train.py[line:525] - INFO: Epoch 463 validation result: mIoU 58.62, best mIoU 59.07
2024-09-10 06:15:34,610 - train.py[line:541] - INFO: Avg train time: 74.92s, avg eval time: 449.07s, left eval count: 37, ETA: 2024-09-10 11:38:42
2024-09-10 06:16:39,151 - train.py[line:384] - INFO: Epoch 464/500 Iter 100/100: lr=5.6216e-06 loss=0.0266 total_loss=0.0308
2024-09-10 06:24:07,813 - train.py[line:525] - INFO: Epoch 464 validation result: mIoU 58.73, best mIoU 59.07
2024-09-10 06:24:07,814 - train.py[line:541] - INFO: Avg train time: 70.29s, avg eval time: 448.90s, left eval count: 36, ETA: 2024-09-10 11:35:38
2024-09-10 06:25:26,152 - train.py[line:384] - INFO: Epoch 465/500 Iter 100/100: lr=5.4809e-06 loss=0.0458 total_loss=0.0307
2024-09-10 06:32:54,981 - train.py[line:525] - INFO: Epoch 465 validation result: mIoU 58.75, best mIoU 59.07
2024-09-10 06:32:54,981 - train.py[line:541] - INFO: Avg train time: 73.04s, avg eval time: 448.87s, left eval count: 35, ETA: 2024-09-10 11:37:21
2024-09-10 06:34:10,198 - train.py[line:384] - INFO: Epoch 466/500 Iter 100/100: lr=5.3398e-06 loss=0.0300 total_loss=0.0309
2024-09-10 06:41:38,542 - train.py[line:525] - INFO: Epoch 466 validation result: mIoU 58.8, best mIoU 59.07
2024-09-10 06:41:38,543 - train.py[line:541] - INFO: Avg train time: 73.46s, avg eval time: 448.66s, left eval count: 34, ETA: 2024-09-10 11:37:30
2024-09-10 06:42:55,259 - train.py[line:384] - INFO: Epoch 467/500 Iter 100/100: lr=5.1983e-06 loss=0.0292 total_loss=0.0308
2024-09-10 06:50:23,908 - train.py[line:525] - INFO: Epoch 467 validation result: mIoU 58.75, best mIoU 59.07
2024-09-10 06:50:23,909 - train.py[line:541] - INFO: Avg train time: 74.26s, avg eval time: 448.66s, left eval count: 33, ETA: 2024-09-10 11:38:00
2024-09-10 06:51:34,209 - train.py[line:384] - INFO: Epoch 468/500 Iter 100/100: lr=5.0563e-06 loss=0.0280 total_loss=0.0292
2024-09-10 06:59:03,208 - train.py[line:525] - INFO: Epoch 468 validation result: mIoU 58.79, best mIoU 59.07
2024-09-10 06:59:03,209 - train.py[line:541] - INFO: Avg train time: 72.25s, avg eval time: 448.79s, left eval count: 32, ETA: 2024-09-10 11:36:56
2024-09-10 07:00:17,991 - train.py[line:384] - INFO: Epoch 469/500 Iter 100/100: lr=4.9139e-06 loss=0.0207 total_loss=0.0303
2024-09-10 07:07:47,435 - train.py[line:525] - INFO: Epoch 469 validation result: mIoU 58.65, best mIoU 59.07
2024-09-10 07:07:47,435 - train.py[line:541] - INFO: Avg train time: 72.79s, avg eval time: 449.05s, left eval count: 31, ETA: 2024-09-10 11:37:24
2024-09-10 07:09:09,925 - train.py[line:384] - INFO: Epoch 470/500 Iter 100/100: lr=4.7711e-06 loss=0.0206 total_loss=0.0300
2024-09-10 07:16:38,319 - train.py[line:525] - INFO: Epoch 470 validation result: mIoU 58.67, best mIoU 59.07
2024-09-10 07:16:38,320 - train.py[line:541] - INFO: Avg train time: 76.13s, avg eval time: 448.79s, left eval count: 30, ETA: 2024-09-10 11:39:05
2024-09-10 07:17:59,560 - train.py[line:384] - INFO: Epoch 471/500 Iter 100/100: lr=4.6278e-06 loss=0.0427 total_loss=0.0305
2024-09-10 07:25:28,914 - train.py[line:525] - INFO: Epoch 471 validation result: mIoU 58.73, best mIoU 59.07
2024-09-10 07:25:28,915 - train.py[line:541] - INFO: Avg train time: 77.63s, avg eval time: 449.02s, left eval count: 29, ETA: 2024-09-10 11:40:01
2024-09-10 07:26:50,513 - train.py[line:384] - INFO: Epoch 472/500 Iter 100/100: lr=4.4839e-06 loss=0.0357 total_loss=0.0303
2024-09-10 07:34:18,369 - train.py[line:525] - INFO: Epoch 472 validation result: mIoU 58.71, best mIoU 59.07
2024-09-10 07:34:18,370 - train.py[line:541] - INFO: Avg train time: 78.71s, avg eval time: 448.55s, left eval count: 28, ETA: 2024-09-10 11:40:21
2024-09-10 07:35:33,516 - train.py[line:384] - INFO: Epoch 473/500 Iter 100/100: lr=4.3396e-06 loss=0.0261 total_loss=0.0299
2024-09-10 07:43:03,497 - train.py[line:525] - INFO: Epoch 473 validation result: mIoU 58.75, best mIoU 59.07
2024-09-10 07:43:03,498 - train.py[line:541] - INFO: Avg train time: 76.88s, avg eval time: 449.12s, left eval count: 27, ETA: 2024-09-10 11:39:45
2024-09-10 07:44:11,940 - train.py[line:384] - INFO: Epoch 474/500 Iter 100/100: lr=4.1947e-06 loss=0.0359 total_loss=0.0302
2024-09-10 07:51:40,892 - train.py[line:525] - INFO: Epoch 474 validation result: mIoU 58.76, best mIoU 59.07
2024-09-10 07:51:40,893 - train.py[line:541] - INFO: Avg train time: 73.03s, avg eval time: 449.05s, left eval count: 26, ETA: 2024-09-10 11:37:55
2024-09-10 07:52:55,501 - train.py[line:384] - INFO: Epoch 475/500 Iter 100/100: lr=4.0493e-06 loss=0.0476 total_loss=0.0301
2024-09-10 08:00:24,879 - train.py[line:525] - INFO: Epoch 475 validation result: mIoU 58.74, best mIoU 59.07
2024-09-10 08:00:24,880 - train.py[line:541] - INFO: Avg train time: 73.21s, avg eval time: 449.18s, left eval count: 25, ETA: 2024-09-10 11:38:04
2024-09-10 08:01:40,784 - train.py[line:384] - INFO: Epoch 476/500 Iter 100/100: lr=3.9033e-06 loss=0.0262 total_loss=0.0306
2024-09-10 08:09:09,926 - train.py[line:525] - INFO: Epoch 476 validation result: mIoU 58.68, best mIoU 59.07
2024-09-10 08:09:09,926 - train.py[line:541] - INFO: Avg train time: 73.87s, avg eval time: 449.17s, left eval count: 24, ETA: 2024-09-10 11:38:22
2024-09-10 08:10:26,447 - train.py[line:384] - INFO: Epoch 477/500 Iter 100/100: lr=3.7567e-06 loss=0.0237 total_loss=0.0302
2024-09-10 08:17:56,172 - train.py[line:525] - INFO: Epoch 477 validation result: mIoU 58.71, best mIoU 59.07
2024-09-10 08:17:56,173 - train.py[line:541] - INFO: Avg train time: 74.51s, avg eval time: 449.39s, left eval count: 23, ETA: 2024-09-10 11:38:45
2024-09-10 08:19:16,997 - train.py[line:384] - INFO: Epoch 478/500 Iter 100/100: lr=3.6094e-06 loss=0.0240 total_loss=0.0295
2024-09-10 08:26:45,928 - train.py[line:525] - INFO: Epoch 478 validation result: mIoU 58.76, best mIoU 59.07
2024-09-10 08:26:45,929 - train.py[line:541] - INFO: Avg train time: 76.58s, avg eval time: 449.21s, left eval count: 22, ETA: 2024-09-10 11:39:33
2024-09-10 08:27:57,659 - train.py[line:384] - INFO: Epoch 479/500 Iter 100/100: lr=3.4615e-06 loss=0.0259 total_loss=0.0301
2024-09-10 08:35:27,382 - train.py[line:525] - INFO: Epoch 479 validation result: mIoU 58.79, best mIoU 59.07
2024-09-10 08:35:27,383 - train.py[line:541] - INFO: Avg train time: 74.13s, avg eval time: 449.41s, left eval count: 21, ETA: 2024-09-10 11:38:41
2024-09-10 08:36:44,039 - train.py[line:384] - INFO: Epoch 480/500 Iter 100/100: lr=3.3128e-06 loss=0.0257 total_loss=0.0307
2024-09-10 08:44:13,943 - train.py[line:525] - INFO: Epoch 480 validation result: mIoU 58.74, best mIoU 59.07
2024-09-10 08:44:13,944 - train.py[line:541] - INFO: Avg train time: 74.66s, avg eval time: 449.61s, left eval count: 20, ETA: 2024-09-10 11:38:59
2024-09-10 08:45:21,128 - train.py[line:384] - INFO: Epoch 481/500 Iter 100/100: lr=3.1635e-06 loss=0.0370 total_loss=0.0307
2024-09-10 08:52:50,584 - train.py[line:525] - INFO: Epoch 481 validation result: mIoU 58.79, best mIoU 59.07
2024-09-10 08:52:50,585 - train.py[line:541] - INFO: Avg train time: 71.20s, avg eval time: 449.55s, left eval count: 19, ETA: 2024-09-10 11:37:44
2024-09-10 08:54:10,760 - train.py[line:384] - INFO: Epoch 482/500 Iter 100/100: lr=3.0133e-06 loss=0.0389 total_loss=0.0308
2024-09-10 09:01:40,248 - train.py[line:525] - INFO: Epoch 482 validation result: mIoU 58.73, best mIoU 59.07
2024-09-10 09:01:40,249 - train.py[line:541] - INFO: Avg train time: 74.30s, avg eval time: 449.52s, left eval count: 18, ETA: 2024-09-10 11:38:49
2024-09-10 09:02:53,532 - train.py[line:384] - INFO: Epoch 483/500 Iter 100/100: lr=2.8623e-06 loss=0.0269 total_loss=0.0294
2024-09-10 09:10:22,281 - train.py[line:525] - INFO: Epoch 483 validation result: mIoU 58.75, best mIoU 59.07
2024-09-10 09:10:22,282 - train.py[line:541] - INFO: Avg train time: 73.39s, avg eval time: 449.21s, left eval count: 17, ETA: 2024-09-10 11:38:26
2024-09-10 09:11:41,796 - train.py[line:384] - INFO: Epoch 484/500 Iter 100/100: lr=2.7104e-06 loss=0.0246 total_loss=0.0293
2024-09-10 09:19:11,432 - train.py[line:525] - INFO: Epoch 484 validation result: mIoU 58.78, best mIoU 59.07
2024-09-10 09:19:11,433 - train.py[line:541] - INFO: Avg train time: 75.32s, avg eval time: 449.38s, left eval count: 16, ETA: 2024-09-10 11:39:06
2024-09-10 09:20:23,464 - train.py[line:384] - INFO: Epoch 485/500 Iter 100/100: lr=2.5575e-06 loss=0.0319 total_loss=0.0294
2024-09-10 09:27:53,402 - train.py[line:525] - INFO: Epoch 485 validation result: mIoU 58.73, best mIoU 59.07
2024-09-10 09:27:53,403 - train.py[line:541] - INFO: Avg train time: 73.58s, avg eval time: 449.61s, left eval count: 15, ETA: 2024-09-10 11:38:41
2024-09-10 09:29:14,646 - train.py[line:384] - INFO: Epoch 486/500 Iter 100/100: lr=2.4037e-06 loss=0.0253 total_loss=0.0302
2024-09-10 09:36:43,239 - train.py[line:525] - INFO: Epoch 486 validation result: mIoU 58.72, best mIoU 59.07
2024-09-10 09:36:43,240 - train.py[line:541] - INFO: Avg train time: 76.09s, avg eval time: 449.20s, left eval count: 14, ETA: 2024-09-10 11:39:17
2024-09-10 09:38:01,653 - train.py[line:384] - INFO: Epoch 487/500 Iter 100/100: lr=2.2487e-06 loss=0.0355 total_loss=0.0297
2024-09-10 09:45:31,065 - train.py[line:525] - INFO: Epoch 487 validation result: mIoU 58.78, best mIoU 59.07
2024-09-10 09:45:31,066 - train.py[line:541] - INFO: Avg train time: 76.57s, avg eval time: 449.29s, left eval count: 13, ETA: 2024-09-10 11:39:27
2024-09-10 09:46:38,994 - train.py[line:384] - INFO: Epoch 488/500 Iter 100/100: lr=2.0925e-06 loss=0.0218 total_loss=0.0299
2024-09-10 09:54:08,344 - train.py[line:525] - INFO: Epoch 488 validation result: mIoU 58.76, best mIoU 59.07
2024-09-10 09:54:08,345 - train.py[line:541] - INFO: Avg train time: 72.66s, avg eval time: 449.31s, left eval count: 12, ETA: 2024-09-10 11:38:31
2024-09-10 09:55:31,308 - train.py[line:384] - INFO: Epoch 489/500 Iter 100/100: lr=1.9350e-06 loss=0.0283 total_loss=0.0289
2024-09-10 10:03:01,445 - train.py[line:525] - INFO: Epoch 489 validation result: mIoU 58.76, best mIoU 59.07
2024-09-10 10:03:01,445 - train.py[line:541] - INFO: Avg train time: 76.20s, avg eval time: 449.64s, left eval count: 11, ETA: 2024-09-10 11:39:25
2024-09-10 10:04:18,479 - train.py[line:384] - INFO: Epoch 490/500 Iter 100/100: lr=1.7761e-06 loss=0.0279 total_loss=0.0294
2024-09-10 10:11:46,832 - train.py[line:525] - INFO: Epoch 490 validation result: mIoU 58.74, best mIoU 59.07
2024-09-10 10:11:46,833 - train.py[line:541] - INFO: Avg train time: 75.99s, avg eval time: 449.13s, left eval count: 10, ETA: 2024-09-10 11:39:18
2024-09-10 10:13:02,423 - train.py[line:384] - INFO: Epoch 491/500 Iter 100/100: lr=1.6156e-06 loss=0.0271 total_loss=0.0302
2024-09-10 10:20:31,780 - train.py[line:525] - INFO: Epoch 491 validation result: mIoU 58.78, best mIoU 59.07
2024-09-10 10:20:31,781 - train.py[line:541] - INFO: Avg train time: 75.29s, avg eval time: 449.22s, left eval count: 9, ETA: 2024-09-10 11:39:12
2024-09-10 10:21:42,921 - train.py[line:384] - INFO: Epoch 492/500 Iter 100/100: lr=1.4533e-06 loss=0.0291 total_loss=0.0293
2024-09-10 10:29:11,936 - train.py[line:525] - INFO: Epoch 492 validation result: mIoU 58.7, best mIoU 59.07
2024-09-10 10:29:11,937 - train.py[line:541] - INFO: Avg train time: 73.13s, avg eval time: 449.14s, left eval count: 8, ETA: 2024-09-10 11:38:50
2024-09-10 10:30:32,186 - train.py[line:384] - INFO: Epoch 493/500 Iter 100/100: lr=1.2889e-06 loss=0.0372 total_loss=0.0295
2024-09-10 10:38:01,015 - train.py[line:525] - INFO: Epoch 493 validation result: mIoU 58.71, best mIoU 59.07
2024-09-10 10:38:01,016 - train.py[line:541] - INFO: Avg train time: 75.52s, avg eval time: 449.01s, left eval count: 7, ETA: 2024-09-10 11:39:12
2024-09-10 10:39:15,464 - train.py[line:384] - INFO: Epoch 494/500 Iter 100/100: lr=1.1222e-06 loss=0.0232 total_loss=0.0296
2024-09-10 10:46:44,190 - train.py[line:525] - INFO: Epoch 494 validation result: mIoU 58.75, best mIoU 59.07
2024-09-10 10:46:44,190 - train.py[line:541] - INFO: Avg train time: 74.57s, avg eval time: 448.90s, left eval count: 6, ETA: 2024-09-10 11:39:05
2024-09-10 10:48:02,501 - train.py[line:384] - INFO: Epoch 495/500 Iter 100/100: lr=9.5265e-07 loss=0.0470 total_loss=0.0304
2024-09-10 10:55:31,825 - train.py[line:525] - INFO: Epoch 495 validation result: mIoU 58.72, best mIoU 59.07
2024-09-10 10:55:31,826 - train.py[line:541] - INFO: Avg train time: 75.70s, avg eval time: 449.07s, left eval count: 5, ETA: 2024-09-10 11:39:15
2024-09-10 10:56:39,204 - train.py[line:384] - INFO: Epoch 496/500 Iter 100/100: lr=7.7967e-07 loss=0.0203 total_loss=0.0292
2024-09-10 11:04:09,723 - train.py[line:525] - INFO: Epoch 496 validation result: mIoU 58.76, best mIoU 59.07
2024-09-10 11:04:09,724 - train.py[line:541] - INFO: Avg train time: 71.97s, avg eval time: 449.65s, left eval count: 4, ETA: 2024-09-10 11:38:56
2024-09-10 11:05:26,927 - train.py[line:384] - INFO: Epoch 497/500 Iter 100/100: lr=6.0227e-07 loss=0.0253 total_loss=0.0294
2024-09-10 11:12:56,040 - train.py[line:525] - INFO: Epoch 497 validation result: mIoU 58.73, best mIoU 59.07
2024-09-10 11:12:56,041 - train.py[line:541] - INFO: Avg train time: 73.64s, avg eval time: 449.43s, left eval count: 3, ETA: 2024-09-10 11:39:05
2024-09-10 11:14:11,191 - train.py[line:384] - INFO: Epoch 498/500 Iter 100/100: lr=4.1875e-07 loss=0.0353 total_loss=0.0294
2024-09-10 11:21:41,254 - train.py[line:525] - INFO: Epoch 498 validation result: mIoU 58.76, best mIoU 59.07
2024-09-10 11:21:41,255 - train.py[line:541] - INFO: Avg train time: 73.78s, avg eval time: 449.69s, left eval count: 2, ETA: 2024-09-10 11:39:08
2024-09-10 11:22:52,528 - train.py[line:384] - INFO: Epoch 499/500 Iter 100/100: lr=2.2541e-07 loss=0.0222 total_loss=0.0303
2024-09-10 11:30:21,831 - train.py[line:525] - INFO: Epoch 499 validation result: mIoU 58.75, best mIoU 59.07
2024-09-10 11:30:21,832 - train.py[line:541] - INFO: Avg train time: 72.44s, avg eval time: 449.53s, left eval count: 1, ETA: 2024-09-10 11:39:03
2024-09-10 11:31:33,777 - train.py[line:384] - INFO: Epoch 500/500 Iter 100/100: lr=3.5406e-09 loss=0.0275 total_loss=0.0307
2024-09-10 11:39:02,969 - train.py[line:525] - INFO: Epoch 500 validation result: mIoU 58.76, best mIoU 59.07
2024-09-10 11:39:02,970 - train.py[line:541] - INFO: Avg train time: 71.90s, avg eval time: 449.40s, left eval count: 0, ETA: 2024-09-10 11:39:02
